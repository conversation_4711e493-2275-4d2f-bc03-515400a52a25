/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-11 10:04:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-09 16:29:09
 * @FilePath: \ems_manage\src\api\device.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

export const getDeviceList = () => {
  return request({
    url: '/deviceList',
    method: 'get'
  })
}

export const getDeviceData = (queryInfo) => {
  return request({
    url: '/devicesData',
    method: 'get',
    params: queryInfo
  })
}

// 获取告警总数
export const getFaultPageInfo = (queryInfo) => {
  return request({
    url: '/faultData/pageInfo',
    method: 'get',
    params: queryInfo
  })
}

// 获取告警数据
export const getFaultData = (queryInfo) => {
  return request({
    url: '/faultData',
    method: 'post',
    data: JSON.stringify(queryInfo)
  })
}

// 获取电池数据
export const batteryCluster = (queryInfo) => {
  return request({
    url: '/batteryCluster',
    method: 'get',
    params: queryInfo
  })
}
export const batteryData = (queryInfo) => {
  return request({
    url: '/batteryData',
    method: 'get',
    params: queryInfo
  })
}

// 获取当前实时告警数
export const getCurrentAlarm = () => {
  return request({
    url: '/getCurrentAlarm',
    method: 'get'
  })
}
export const getAnalyticsData = (data) => {
  return request({
    url: '/getAnalyticsData',
    method: 'post',
    data
  })
}

// 录波导出
export const exportAlarmRecordFile = (data) => {
  return request({
    url: '/exportAlarmRecordFile',
    method: 'post',
    data,
    responseType: "blob",
  })
}

// 告警导出
export const exportAlarmData = (data) => {
  return request({
    url: '/exportFaultData',
    method: 'post',
    data,
    responseType: "blob",
  })
}