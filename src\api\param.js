import { request } from './index'

// 下发参数
export const powerSet = (data) => {
  return request({
    url: '/powerSet',
    method: 'post',
    data
  })
}

// 获取参数
export const powerParams = () => {
  return request({
    url: '/powerParams',
    method: 'get'
  })
}

// 获取削峰填谷
export const peakcutTemplates = () => {
  return request({
    url: '/peakcutTemplates',
    method: 'get'
  })
}

// 获取策略信息
export const peakcutInformation = () => {
  return request({
    url: '/peakcutInformation',
    method: 'get'
  })
}

// 下发策略
export const peakcutInfoSet = (data) => {
  return request({
    url: '/peakcutInfoSet',
    method: 'post',
    data
  })
}

// 修改方案
export const peakcutTpEdit = (data) => {
  return request({
    url: '/peakcutTpEdit',
    method: 'post',
    data
  })
}

// 删除方案
export const peakcutTpDelete = (data) => {
  return request({
    url: '/peakcutTpDelete',
    method: 'post',
    data
  })
}

// 删除方案
export const peakcutTpAdd = (data) => {
  return request({
    url: '/peakcutTpAdd',
    method: 'post',
    data
  })
}

// 获取运行策略参数
export const hybridEnergyInfo = () => {
  return request({
    url: '/hybridEnergyInfo',
    method: 'get'
  })
}

// 设置运行策略参数
export const hybridEnergySet = (data) => {
  return request({
    url: '/hybridEnergySet',
    method: 'post',
    data
  })
}

// 获取策略工单列表
export const getStrategyList = () => {
  return request({
    url: '/getStrategyList',
    method: 'post'
  })
}

// 获取具体工单详细信息
export const getStrategyData = (data) => {
  return request({
    url: '/getStrategyData',
    method: 'post',
    data
  })
}

// 激活工单策略
export const changeStrategy = (data) => {
  return request({
    url: '/changeStrategy',
    method: 'post',
    data
  })
}

// 下发
export const postStrategyCommand = (data) => {
  return request({
    url: '/postStrategyCommand',
    method: 'post',
    data
  })
}

// 策略配置
export const updateStrategyConfig = (data) => {
  return request({
    url: '/updateStrategyConfig',
    method: 'post',
    data
  })
}

/**
 * 电价信息
 */
export const countryList = () => {
  return request({
    url: '/ep/regions',
    method: 'get'
  })
}

export const countryData = (data) => {
  return request({
    url: '/ep/data',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}