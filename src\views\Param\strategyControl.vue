<script setup>
import {
  toRefs,
  computed,
  defineAsyncComponent,
  ref,
  watch,
  getCurrentInstance
} from 'vue'
import { useParamStore } from '@/store/module/param'
import { useGlobalStore } from '@/store/global'
import { useUserStore } from '@/store/module/user'
import { useDisplay } from 'vuetify'
import { useI18n } from 'vue-i18n'

import { ElTree } from 'element-plus'

const Strategy308 = defineAsyncComponent(() => import('./strategy308.vue'))
const StrategyPeakcut = defineAsyncComponent(() => import('./strategy.vue'))
const StrategyLoaclTest = defineAsyncComponent(() =>
  import('./Price/index.vue')
)

const { mobile } = useDisplay()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const coms = ref([
  {
    label: 'Strategy_PD24308',
    value: Strategy308
  },
  {
    label: 'Strategy_peakcut',
    value: StrategyPeakcut
  },
  {
    label: 'LocalTestStrategy',
    value: StrategyLoaclTest
  }
])
const { userInfo } = toRefs(useUserStore())
const isUser = computed(() => userInfo.value.permission_level == 2)
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { strategyList } = toRefs(useParamStore())
useParamStore()
  .getStrategyListFn()
  .then((res) => {
    let data = strategyList.value.find((item) => item.loaded == 1)
    currentStrategy.value = {
      name: data?.name,
      loaded: data?.loaded,
      component: coms.value.find((item1) => item1.label == data?.name)?.value
    }
  })
  .catch((error) => {
    snackbar.value = true
    snackbarText.value = error
  })
const handleChangeClick = (node, data) => {
  useParamStore()
    .changeStrategyFn(JSON.stringify({ name: data.name }))
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('已激活')
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
const currentStrategy = ref({
  name: undefined,
  loaded: 0,
  component: undefined
})

/**
 * 树
 */
const search = ref(null)
const caseSensitive = ref(false)
watch(search, (val) => {
  proxy.$refs.treeRef.filter(val)
})

const filterNode = (value, data) => {
  const searchValue = caseSensitive.value ? value : value?.toLowerCase()
  if (!searchValue) return true
  return data.title.includes(searchValue)
}
const handleNodeClick = (node) => {
  currentStrategy.value.name = node.name
  currentStrategy.value.loaded = node.loaded
  currentStrategy.value.component = coms.value.find(
    (item1) => item1.label == node.name
  )?.value
}
const drawer = ref(false)
const currentNodeKey = computed(() => {
  return currentStrategy.value.name
})
/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  search.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

const emptyText = ref(t('未激活策略，请联系管理人员激活。'))
</script>

<template>
  <div class="pa-4 h-100 overflow-hidden d-flex" v-if="!isUser">
    <v-col
      cols="12"
      lg="3"
      md="12"
      sm="12"
      xs="12"
      class="h-100"
      v-if="!mobile"
    >
      <v-card elevation="4" class="rounded-lg h-full">
        <div class="d-flex justify-between align-center px-4 py-2">
          <div class="text-h6">{{ $t('策略列表') }}</div>
        </div>
        <v-sheet class="px-4 bg-primary-lighten-2">
          <v-text-field
            v-model="search"
            clear-icon="mdi-close-circle-outline"
            :label="$t('搜索')"
            clearable
            dark
            flat
            hide-details
            solo-inverted
            @click:control="handleShow($event, search)"
          ></v-text-field>
          <v-checkbox
            v-model="caseSensitive"
            :label="$t('区分大小写搜索')"
            dark
            hide-details
          ></v-checkbox>
        </v-sheet>
        <el-tree
          :data="strategyList"
          node-key="name"
          ref="treeRef"
          :props="{
            label: 'name'
          }"
          :indent="25"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          :current-node-key="currentNodeKey"
        >
          <template #default="{ node, data }">
            <div class="d-flex justify-space-between align-center w-100 pr-6">
              <div class="d-flex justify-space-between align-center">
                <v-tooltip
                  :text="data.loaded == 0 ? $t('未激活') : $t('已激活')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      :class="[
                        'mr-2',
                        data.loaded == 0 ? 'bg-ccc' : 'bg-333',
                        'loaded-cir'
                      ]"
                      v-bind="props"
                    /> </template
                ></v-tooltip>
                <span>{{ node.label }}</span>
              </div>
              <v-btn @click.stop="handleChangeClick(node, data)">{{
                $t('激活')
              }}</v-btn>
            </div>
          </template>
        </el-tree>
      </v-card>
    </v-col>
    <v-navigation-drawer v-model="drawer" temporary width="300" v-else>
      <div class="d-flex justify-between align-center px-4 py-2">
        <div class="text-h6">{{ $t('策略列表') }}</div>
      </div>
      <v-sheet class="px-4 bg-primary-lighten-2">
        <v-text-field
          v-model="search"
          clear-icon="mdi-close-circle-outline"
          :label="$t('搜索')"
          clearable
          dark
          flat
          hide-details
          solo-inverted
          @click:control="handleShow($event, search)"
        ></v-text-field>
        <v-checkbox
          v-model="caseSensitive"
          :label="$t('区分大小写搜索')"
          dark
          hide-details
        ></v-checkbox>
      </v-sheet>
      <el-tree
        :data="strategyList"
        node-key="name"
        ref="treeRef"
        :props="{
          label: 'name'
        }"
        :indent="25"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        :current-node-key="currentNodeKey"
      >
        <template #default="{ node, data }">
          <div class="d-flex justify-space-between align-center w-100 pr-6">
            <div class="d-flex justify-space-between align-center">
              <v-tooltip
                :text="data.loaded == 0 ? $t('未激活') : $t('已激活')"
                location="top"
              >
                <template v-slot:activator="{ props }">
                  <div
                    :class="[
                      'mr-2',
                      data.loaded == 0 ? 'bg-ccc' : 'bg-333',
                      'loaded-cir'
                    ]"
                    v-bind="props"
                  /> </template
              ></v-tooltip>
              <span>{{ node.label }}</span>
            </div>
            <v-btn @click.stop="handleChangeClick(node, data)">{{
              $t('激活')
            }}</v-btn>
          </div>
        </template>
      </el-tree>
    </v-navigation-drawer>
    <v-col cols="12" lg="9" md="12" sm="12" xs="12" class="h-100">
      <v-card elevation="4" class="rounded-lg h-100 overflow-y-auto pa-4 pt-0">
        <div class="d-flex justify-between align-center mt-2" v-if="mobile">
          <div class="d-flex justify-between align-center">
            <div class="text-h6">{{ $t('策略管理') }}</div>
          </div>
          <v-btn
            height="48px"
            class="mr-4"
            color="primary"
            @click="drawer = !drawer"
            >{{ $t('选择策略') }}</v-btn
          >
        </div>
        <template v-if="currentStrategy.name">
          <component
            :is="currentStrategy.component"
            v-model="currentStrategy.name"
          />
        </template>
        <empty v-else />
      </v-card>
    </v-col>
  </div>
  <div class="pa-6 overflow-auto" v-else>
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <div class="d-flex justify-between align-center">
        <div class="text-h6">{{ $t('策略管理') }}</div>
      </div>
      <template v-if="currentStrategy.name">
        <component
          :is="currentStrategy.component"
          v-model="currentStrategy.name"
        />
      </template>
      <empty v-model="emptyText" v-else />
    </v-card>
  </div>
</template>

<style lang="scss" scoped>
.loaded-cir {
  width: 15px;
  height: 15px;
  border-radius: 100px;
}
.bg-ccc {
  background-color: #ccc;
}
.bg-333 {
  background-color: #333;
}
:deep(.el-tree) {
  --el-tree-node-content-height: 48px;
  --el-tree-node-hover-bg-color: #f6f6f6;
  --el-tree-text-color: #333;
  --el-tree-expand-icon-color: #333;
  color: #333;
  font-size: 16px;
  .el-tree-node__expand-icon {
    font-size: 16px;
  }
  .el-tree-node__content {
    /* padding: 4px 16px !important; */
  }
  .is-current {
    font-weight: bold;
    color: #333;
    background-color: #f6f6f6;
  }
}
</style>
