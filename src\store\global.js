/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:11:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-23 16:48:36
 * @FilePath: \ems_manage\src\store\global.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getLanguage, languageSet } from '@/api/global'

export const useGlobalStore = defineStore(
  'global',
  () => {
    const snackbar = ref(false)
    const snackbarText = ref('')

    /**
     * 语言
     */
    const lang = ref()
    const getLanguageFn = async () => {
      const res = await getLanguage()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      if (res.data.language == 'zh') lang.value = 'zhHans'
      else lang.value = res.data.language
    }
    const languageSetFn = async (data) => {
      let form = data.language == 'zhHans' ? { language: 'zh' } : data
      const res = await languageSet(JSON.stringify(form))
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
    }

    /**
     * 键盘
     */
    const showKeyboard = ref(false)
    const currentInput = ref(null)
    const confirmCall = ref(null)
    const keyboardDialog = ref(false)
    const keyboardInput = ref(null)
    const keyboardInputValue = ref('')
    const keyboardMode = ref('di_gt')
    const keyboardRange = ref([])
    const isShowKeyboard = ref(true)

    return {
      snackbar,
      snackbarText,
      lang,
      getLanguageFn,
      languageSetFn,
      showKeyboard,
      currentInput,
      confirmCall,
      keyboardDialog,
      keyboardInput,
      keyboardInputValue,
      keyboardMode,
      keyboardRange,
      isShowKeyboard
    }
  },
  {
    persist: true
  }
)
