<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-01-17 11:01:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-21 19:25:23
 * @FilePath: \ems_manage\src\components\data-item\src\data-item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { computed, ref } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { useI18n } from 'vue-i18n'

const prop = defineProps({
  item: {
    type: Object,
    default: () => ({
      units: '',
      value: '',
      point_name: ''
    })
  },
  isStatus: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

const getStatusTypeFn = (status) => {
  return useDeviceStore().getStatusTypeFn(status)
}

const lineBreak = computed(() => {
  return (string) => {
    let res = {
      text: '',
      status: 0
    }
    if (typeof string == 'string') {
      if (string.indexOf('\n') !== -1) {
        res = {
          text: string?.replace(/\n/g, '<br />'),
          status: 1
        }
      } else {
        res = {
          text: string || '--',
          status: 0
        }
      }
    } else {
      res = {
        text: string || '--',
        status: 0
      }
    }
    return res
  }
})

const dialog = ref(false)
const handleItemClick = (status) => {
  if (!status) return
  dialog.value = true
}
const borderStyle = computed(() => {
  return (hover, status) => {
    if (hover) {
      return status == 1 ? '1px solid #00000080' : '1px solid #e9e9e9'
    } else {
      return status == 1 ? '1px solid #fcca1e' : '1px solid #e9e9e9'
    }
  }
})
</script>

<template>
  <v-hover>
    <template v-slot:default="{ isHovering, props }">
      <v-card
        variant="tonal"
        class="py-3 px-3"
        v-bind="props"
        :elevation="isHovering ? 4 : 0"
        color="grey-darken-1"
        :style="{
          border: borderStyle(isHovering, lineBreak(prop.item.value).status)
        }"
        @click="handleItemClick(lineBreak(prop.item.value).status)"
      >
        <template v-if="isHovering && lineBreak(prop.item.value).status == 1">
          <v-tooltip :text="$t('点击显示更多')" location="top">
            <template v-slot:activator="{ props }">
              <div class="more-mask" v-bind="props">
                <img
                  src="../../../assets/img/more.svg"
                  alt=""
                  style="width: 80px"
                />
              </div> </template
          ></v-tooltip>
        </template>
        <div
          class="flex align-center justify-center"
          :style="{
            color: '#000',
            height: '40px'
          }"
        >
          <template v-if="prop.isStatus">
            <img
              src="../../../assets/img/icon-red.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-if="getStatusTypeFn(item.value) == 2"
            />
            <img
              src="../../../assets/img/icon-enable.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-else-if="getStatusTypeFn(item.value) == 1"
            />
            <img
              src="../../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-else
            />
          </template>
          <template v-else>
            <img
              src="../../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
            />
          </template>
          <span>{{ prop.item.point_name }}</span>
        </div>
        <div
          class="font-600 my-1 flex justify-center overflow-hidden"
          :style="{
            color: '#ffaa43',
            height: '30px',
            lineHeight: '30px'
          }"
        >
          <div v-html="lineBreak(prop.item.value).text"></div>
          <span class="ml-1">{{ prop.item.units }}</span>
          <div v-if="lineBreak(prop.item.value).status == 1">···</div>
        </div>
        <!-- <div
          class="w-100"
          :style="{
            color: '#000',
            textAlign: 'center',
            height: '30px',
            lineHeight: '30px'
          }"
        >
          {{ prop.item.time ? prop.item.time : '--' }}
        </div> -->
      </v-card>
    </template>
  </v-hover>

  <v-dialog v-model="dialog" width="auto">
    <v-card
      class="py-4 px-4 flex flex-column align-center"
      color="#fff"
      width="440"
    >
      <div
        class="flex align-center"
        :style="{
          color: '#000'
        }"
      >
        <template v-if="prop.isStatus">
          <img
            src="../../../assets/img/icon-red.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-if="getStatusTypeFn(item.value) == 2"
          />
          <img
            src="../../../assets/img/icon-enable.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-else-if="getStatusTypeFn(item.value) == 1"
          />
          <img
            src="../../../assets/img/circle.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-else
          />
        </template>
        <template v-else>
          <img
            src="../../../assets/img/circle.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
          />
        </template>
        <span>{{ prop.item.point_name }}</span>
      </div>
      <div
        class="font-600 my-1 flex"
        :style="{
          color: '#ffaa43'
        }"
      >
        <div v-html="lineBreak(prop.item.value).text"></div>
        {{ prop.item.units }}
      </div>
      <div
        :style="{
          color: '#000'
        }"
      >
        {{ prop.item.time ? prop.item.time : '--' }}
      </div>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.more-mask {
  background: #00000080;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
</style>
