<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-23 18:00:19
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, watch, computed } from 'vue'
import dayjs, { formatDate } from '@/utils/date'

const lineData = defineModel('lineData', {
  default: {
    times: [],
    datas: [],
    currency: '',
    name: '',
    date: ''
  }
})
const scheduleData = defineModel('scheduleData', {
  default: {
    times: [],
    datas: [],
    currency: '',
    name: '',
    date: ''
  }
})
const powerData = defineModel('powerData', {
  default: []
})

const options = computed(() => {
  let series = []
  powerData.value.forEach((item) => {
    let data = item.times.map((item2, index2) => {
      return [item2, item.datas[index2]]
    })
    let name = `${item.device_name}_${item.point_id}`
    series.push({
      name,
      type: 'line',
      symbol: 'none',
      data,
      unit: item.unit,
      yAxisIndex: 0,
      xAxisIndex: 0,
      step: 'end'
    })
  })
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10],
      formatter: (params) => {
        let htmlStart = `<div>`
        let oneParams = params.filter((param) => param.axisIndex == 0)
        let twoParams = params.filter((param) => param.axisIndex == 1)
        if (oneParams.length > 0) {
          htmlStart += `<div>${oneParams[0].axisValueLabel}</div>`
          oneParams.forEach((oneParam, oneIndex) => {
            htmlStart += `<div>${oneParam.marker}${oneParam.seriesName}：<span style="font-weight: 600;margin-right: 3px;">${oneParam.value[1]}</span>${powerData.value[oneIndex].unit ?? ''}</div>`
        })
        } 
        if (twoParams.length > 0) {
          htmlStart += `<div style="${oneParams.length > 0 ? 'margin-top: 10px': ''}">${twoParams[0].axisValueLabel}</div>`
          twoParams.forEach((twoParam) => {
            htmlStart += `<div>${twoParam.marker}${twoParam.seriesName}：<span style="font-weight: 600;margin-right: 3px;">${twoParam.value[1]}</span>${twoParam.seriesName.indexOf('Price') !== -1 ? lineData.value?.currency: 'kW'}</div>`
        })
        }
        return htmlStart + '</div>'
      }
    },
    legend: {
      type: 'scroll',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
      },
      itemGap: 30,
      top: 5
    },
    axisPointer: {
      link: [
        {
          xAxisIndex: 'all'
        }
      ]
    },
    grid: [
      {
        left: 70,
        right: 40,
        height: '28%',
        top: '20%'
      },
      {
        left: 70,
        right: 40,
        top: '58%',
        height: '28%'
      }
    ],
    xAxis: [
      // {
      //   axisLine: {
      //     lineStyle: {
      //       // color: '#fff'
      //     }
      //   },
      //   axisTick: {
      //     //y轴刻度线
      //     show: true
      //   },
      //   splitLine: {
      //     //分割线
      //     show: false, //控制分割线是否显示
      //     lineStyle: {
      //       //分割线的样式
      //       color: 'rgba(81, 82, 85, 0.3)',
      //       width: 1,
      //       type: 'solid'
      //     }
      //   },
      //   type: 'category',
      //   boundaryGap: false,
      //   data: lineData.value?.times
      // }
      {
        type: 'time',
        boundaryGap: false,
        axisLine: { onZero: true }
        // data: lineData.value?.times.map((item) => formatDate(item, 'HH:mm'))
        // data: lineData.value?.times,
      },
      {
        gridIndex: 1,
        type: 'time',
        boundaryGap: false,
        axisLine: { onZero: true },
        // data: lineData.value?.times,
        position: 'top'
      }
    ],
    yAxis: [
      {
        // name: `Power(kW)`,
        type: 'value',
        axisLine: {
          lineStyle: {
            // color: '#fff'
          }
        },
        splitLine: {
          //分割线
          show: false, //控制分割线是否显示
          lineStyle: {
            //分割线的样式
            color: 'rgba(81, 82, 85, 0.3)',
            width: 1,
            type: 'solid'
          }
        },
        nameTextStyle: {
          align: 'right'
        },
        minInterval: 5,
        // nameLocation: 'start',
        alignTicks: true
      },
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            // color: '#fff'
          }
        },
        splitLine: {
          //分割线
          show: false, //控制分割线是否显示
          lineStyle: {
            //分割线的样式
            color: 'rgba(81, 82, 85, 0.3)',
            width: 1,
            type: 'solid'
          }
        },
        nameTextStyle: {
          align: 'right'
        },
        minInterval: 5,
        inverse: true,
        gridIndex: 1
      }
    ],
    dataZoom: [
      {
        show: true,
        realtime: true,
        start: 0,
        xAxisIndex: [1],
        type: 'slider'
      },
      {
        show: true,
        realtime: true,
        start: 0,
        xAxisIndex: [0],
        type: 'slider',
        top: '8%'
      },
      {
        type: 'inside',
        realtime: true,
        start: 0,
        xAxisIndex: [1]
      },
      {
        type: 'inside',
        realtime: true,
        start: 0,
        xAxisIndex: [0]
      }
    ],
    series: [
      {
        name: lineData.value?.name + '_' + 'Price',
        type: 'line',
        symbol: 'none',
        // data: lineData.value?.datas,
        data: lineData.value?.times.map((item2, index2) => {
          return [item2, lineData.value?.datas[index2]]
        }),
        date: lineData.value?.date,
        currency: lineData.value?.currency,
        step: 'end',
        yAxisIndex: 1,
        xAxisIndex: 1
      },
      {
        name: 'Power schedule',
        type: 'line',
        symbol: 'none',
        // data: scheduleData.value?.datas,
        data: scheduleData.value?.times.map((item2, index2) => {
          return [item2, scheduleData.value?.datas[index2]]
        }),
        step: 'end',
        yAxisIndex: 1,
        xAxisIndex: 1
      },
      ...series
    ]
  }
})
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
