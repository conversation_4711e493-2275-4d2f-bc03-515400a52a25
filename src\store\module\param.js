import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  powerSet,
  powerParams,
  peakcutTemplates,
  peakcutInformation,
  peakcutInfoSet,
  peakcutTpEdit,
  peakcutTpDelete,
  peakcutTpAdd,
  hybridEnergyInfo,
  hybridEnergySet,
  getStrategyList,
  changeStrategy,
  getStrategyData,
  postStrategyCommand,
  updateStrategyConfig,
  countryList, countryData
} from '@/api/param'

export const useParamStore = defineStore(
  'param',
  () => {
    const powerSetFn = async (data) => {
      const res = await powerSet(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      return res
    }

    const params = ref({})
    const powerParamsFn = async () => {
      const res = await powerParams()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      if (res.data.length) {
        res.data.forEach((item) => {
          params.value[item.name] = item.value
        })
      } else {
        params.value = {
          active_power: '0',
          reactive_power: '0',
          power_factor: '0',
          high_voltage_main_switch: '0',
          oil_engine_switch: '0',
          system_switch: '0',
          policy_enabling: '0',
          mains_switch: '0',
          double_division_control: '1'
        }
      }
    }

    /**
     * 策略
     */
    const templates = ref([])
    const peakcutTemplatesFn = async () => {
      const res = await peakcutTemplates()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      templates.value = res.data.templates
    }
    const strategyInfo = ref({})
    const peakcutInformationFn = async () => {
      const res = await peakcutInformation()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      strategyInfo.value = res.data
    }

    const peakcutInfoSetFn = async (data) => {
      const res = await peakcutInfoSet(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    // 修改方案
    const peakcutTpEditFn = async (data) => {
      const res = await peakcutTpEdit(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    // 删除方案
    const peakcutTpDeleteFn = async (data) => {
      const res = await peakcutTpDelete(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    // 添加方案
    const peakcutTpAddFn = async (data) => {
      const res = await peakcutTpAdd(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }

    /**
     * 运行策略
     */
    const hybridEnergySetFn = async (data) => {
      const res = await hybridEnergySet(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    const hybridInfo = ref({})
    const hybridEnergyInfoFn = async () => {
      const res = await hybridEnergyInfo()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      hybridInfo.value = res.data
    }



    const strategyList = ref([])
    const getStrategyListFn = async () => {
      const res = await getStrategyList()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      strategyList.value = res.data.Strategys
    }
    const changeStrategyFn = async (data) => {
      const res = await changeStrategy(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      getStrategyListFn()
    }
    const strategyData = ref({})
    const getStrategyDataFn = async (data) => {
      const res = await getStrategyData(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      strategyData.value = res.data.config
    }
    const postStrategyCommandFn = async (data) => {
      const res = await postStrategyCommand(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    const updateStrategyConfigFn = async (data) => {
      const res = await updateStrategyConfig(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }
    const countryOptions = ref([])
    const countryListFn = async () => {
      const res = await countryList()
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      countryOptions.value = res.data
    }
    const countryDatas = ref()
    const countryDataFn = async (data) => {
      const res = await countryData(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      countryDatas.value = res.data
    }

    return {
      powerSetFn,
      params,
      powerParamsFn,
      templates,
      peakcutTemplatesFn,
      strategyInfo,
      peakcutInformationFn,
      peakcutInfoSetFn,
      peakcutTpEditFn,
      peakcutTpDeleteFn,
      peakcutTpAddFn,
      hybridInfo,
      hybridEnergyInfoFn,
      hybridEnergySetFn,
      strategyList,
      getStrategyListFn,
      changeStrategyFn,
      strategyData,
      getStrategyDataFn,
      postStrategyCommandFn,
      updateStrategyConfigFn,
      countryOptions,
      countryListFn,
      countryDatas,
      countryDataFn
    }
  }
  // {
  //   persist: true
  // }
)
