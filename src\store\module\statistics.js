/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-24 14:31:59
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-23 15:02:22
 * @FilePath: \ems_manage\src\store\module\statistics.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE${}
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getPowerData, exportData, analyseExport } from '@/api/statistics'
import dayjs from '@/utils/date'
import { getDeviceData, batteryCluster, batteryData, getAnalyticsData } from '@/api/device'
import { maxBy, minBy, isEmpty } from 'lodash-es'
import { i18n } from '@/locale'
import { handleExport, sortFn } from '@/utils'
import { useGlobalStore } from './../global';

export const useStatisticsStore = defineStore(
  'statistics',
  () => {
    const powerData = ref({})
    const powerQueryInfo = ref({})
    const getPowerDataFn = async (queryInfo) => {
      const res = await getPowerData({
        ...powerQueryInfo.value
      })
      if (res.code !== 200) {
        useGlobalStore().snackbar = true
        useGlobalStore().snackbarText = res.msg
        return new Error(res.msg)
      }
      // if (!res.data.energy.length) {
      //   powerData.value.tableData = []
      //   powerData.value.chartData = {}
      //   return
      // }
      if (queryInfo.type == 0) {
        let nData = {}
        let pData = {}
        res.data.energy.hourly_n.forEach(
          (item) => (nData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        res.data.energy.hourly_p.forEach(
          (item) => (pData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        let dataNArr = res.data.energy.hourly_n.map((item) => item[Object.keys(item)[0]])
        let dataPArr = res.data.energy.hourly_p.map((item) => item[Object.keys(item)[0]])
        powerData.value.tableData = [
          {
            name: i18n.global.t('充电量'),
            ...nData,
            sumData: dataNArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          },
          {
            name: i18n.global.t('放电量'),
            ...pData,
            sumData: dataPArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          }
        ]
        powerData.value.chartData = {
          n: dataNArr,
          p: dataPArr
        }
      } else if (queryInfo.type == 1) {
        let days = dayjs(powerQueryInfo.value.date).daysInMonth()
        let nData = {}
        let pData = {}
        res.data.energy.daily_n.forEach(
          (item) => (nData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        res.data.energy.daily_p.forEach(
          (item) => (pData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        let nKeys = Object.keys(nData)
        let pKeys = Object.keys(pData)
        let dataNArr = []
        let dataPArr = []
        for (let i = 1; i <= days; i++) {
          if (!nKeys.includes(`day_${i}`)) nData[`day_${i}`] = 0
          if (!pKeys.includes(`day_${i}`)) pData[`day_${i}`] = 0
          dataNArr.push(nData[`day_${i}`])
          dataPArr.push(pData[`day_${i}`])
        }
        powerData.value.tableData = [
          {
            name: i18n.global.t('充电量'),
            ...nData,
            sumData: dataNArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          },
          {
            name: i18n.global.t('放电量'),
            ...pData,
            sumData: dataPArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          }
        ]
        powerData.value.chartData = {
          n: dataNArr,
          p: dataPArr
        }
      } else if (queryInfo.type == 2) {
        let nData = {}
        let pData = {}
        res.data.energy.monthly_n.forEach(
          (item) => (nData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        res.data.energy.monthly_p.forEach(
          (item) => (pData[Object.keys(item)[0]] = item[Object.keys(item)[0]])
        )
        let nKeys = Object.keys(nData)
        let pKeys = Object.keys(pData)
        let dataNArr = []
        let dataPArr = []
        for (let i = 1; i <= 12; i++) {
          if (!nKeys.includes(`month_${i}`)) nData[`month_${i}`] = 0
          if (!pKeys.includes(`month_${i}`)) pData[`month_${i}`] = 0
          dataNArr.push(nData[`month_${i}`])
          dataPArr.push(pData[`month_${i}`])
        }
        powerData.value.tableData = [
          {
            name: i18n.global.t('充电量'),
            ...nData,
            sumData: dataNArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          },
          {
            name: i18n.global.t('放电量'),
            ...pData,
            sumData: dataPArr
              .reduce((pre, current) => pre + Number(current), 0)
              .toFixed(2)
          }
        ]
        powerData.value.chartData = {
          n: dataNArr,
          p: dataPArr
        }
      }
    }

    const exportDataFn = async (queryInfo) => {
      const res = await exportData({
        start: queryInfo.start,
        end: queryInfo.end,
        deviceId: queryInfo.deviceId,
        flag: queryInfo.flag
      })
      if (!(res instanceof Blob) && res?.code !== 200) {
        useGlobalStore().snackbar = true
        useGlobalStore().snackbarText = res.msg
        return new Error(res.msg)
      }
      handleExport(res, `${queryInfo.deviceName}_${queryInfo.start}_${queryInfo.end}_电量统计报表`)
    }

    /**
     * 数据分析
     */
    const treePointData = ref([])
    const pointData = ref({})
    const dataQueryInfo = ref({
      deviceId: '',
      pointId: '',
      date: ''
    })
    // /devicesData?deviceId=2&pointId=938&date=2024-09-27
    const getTreePointDataFn = async (queryInfo) => {
      treePointData.value = []
      const res = await getDeviceData(queryInfo)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      let resItem = res.data
      // res.data.forEach((resItem) => {
      if (resItem.modules.length) {
        // 有模块
        resItem.modules.forEach((module, moduleIndex) => {
          module.points.forEach((point) => {
            let key = Object.keys(point)[0]
            // if (key == 'analog') {
            point[key].sort(sortFn)
            treePointData.value = [
              ...treePointData.value,
              {
                id: `${module.device_id}_${module.module_name}`,
                title: module.module_name + `${key == 'analog' ? '(' + i18n.global.t('模拟') + ')' : '(' + i18n.global.t('状态') + ')'}`,
                module: true,
                children: point[key].map((item) => {
                  return {
                    id: item.point_id,
                    title: item.point_name,
                    unit: item.unit,
                    leaf: true,
                    device_id: queryInfo.deviceId,
                    module_name: module.module_name,
                    device_name: resItem.device_name
                  }
                })
              }
            ]
            // }
          })
        })
      } else {
        if (!resItem.points || !resItem.points.length) return
        resItem.points.forEach((point) => {
          let key = Object.keys(point)[0]
          // if (key == 'analog') {
          point[key].sort(sortFn)
          point[key].forEach((item) => {
            treePointData.value = [
              ...treePointData.value,
              {
                id: item.point_id,
                title: item.point_name,
                unit: item.unit,
                leaf: true,
                device_id: queryInfo.deviceId,
                module_name: null,
                device_name: resItem.device_name
              }
            ]
          })
          // }
        })
      }
      // })

      return treePointData.value
    }
    const getPointDataFn = async (queryInfo) => {
      try {
        const res = await getAnalyticsData(queryInfo)
        let pointData = {}
        if (res.code !== 200) {
          useGlobalStore().snackbar = true
          useGlobalStore().snackbarText = res.msg
          return new Error(res.msg)
        }
        if (!res.data.points.length) {
          return {
            noBt: 1,
            times: [],
            datas: [],
            tableData: [],
            pointId: []
          }
        }
        // res.forEach((item) => {
        //   if (item.modules.length) {
        //     // 有模块
        //     item.modules.forEach((module) => {
        //       let data = module.points[0].analog
        //       pointData.times = data.map((item) => item.record_time)
        //       pointData.datas = data.map((item) => item.value)
        //       pointData.tableData = [
        //         {
        //           title: '',
        //           max: maxBy(data, 'value').value,
        //           maxTime: maxBy(data, 'value').record_time,
        //           min: minBy(data, 'value').value,
        //           minTime: minBy(data, 'value').record_time
        //         }
        //       ]
        //     })
        //   } else {
        //     if (!item.points || !item.points.length) {
        //       pointData = {
        //         times: [],
        //         datas: [],
        //         tableData: []
        //       }
        //       return
        //     }
        //     let data = item.points[0].analog
        //     pointData.times = data.map((item) => item.record_time)
        //     pointData.datas = data.map((item) => item.value)
        //     pointData.tableData = [
        //       {
        //         title: '',
        //         max: maxBy(data, 'value').value,
        //         maxTime: maxBy(data, 'value').record_time,
        //         min: minBy(data, 'value').value,
        //         minTime: minBy(data, 'value').record_time
        //       }
        //     ]
        //   }
        // })
        let data = {
          noBt: 1,
          datas: [],
          times: [],
          tableData: [],
          pointId: []
        }
        res.data.points.forEach(item => {
          if (item.values.length) {
            data.pointId.push({
              pointId: item.point_id,
              deviceName: item.device_name,
              unit: item.unit
            })
            data.datas.push(item.values.map(val => Number(val.value)))
            data.times.push(item.values.map(val => val.record_time))
            let values = item.values.map(item => {
              item.value = Number(item.value)
              return item
            })
            data.tableData.push(
              {
                title: '',
                max: maxBy(values, 'value').value,
                maxTime: maxBy(values, 'value').record_time,
                min: minBy(values, 'value').value,
                minTime: minBy(values, 'value').record_time
              })
          }
        })
        return data
      } catch (error) {
        useGlobalStore().snackbar = true
        useGlobalStore().snackbarText = error
      }
    }
    // 电池数据
    const clusterData = ref([])
    const clusterPointData = ref({})
    const batteryClusterFn = async (queryInfo) => {
      clusterData.value = []
      const res = await batteryCluster({
        deviceId: queryInfo.deviceId
      })
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      res.data.forEach(item => {
        // if (item.cluster_name.indexOf('#') == -1) {
        clusterData.value.push({
          ...item,
          title: item.cluster_name,
          id: `cluster_${item.cluster_name}`,
          children: [],
          device_class: 'BMSDevice',
          device_id: queryInfo.deviceId,
          cluster: 1,
          device_name: queryInfo.device_name
        })
        // }
      })
      // clusterData.value = res.map((item) => {
      //   return {
      //     ...item,
      //     title: item.cluster_name,
      //     id: `cluster_${item.cluster_name}`,
      //     children: [],
      //     device_class: 'BMSDevice',
      //     device_id: queryInfo.deviceId,
      //     cluster: 1,
      //     device_name: queryInfo.device_name
      //   }
      // })

      return clusterData.value
    }
    const batteryDataFn = async (queryInfo) => {
      const res = await batteryData({
        deviceId: queryInfo.deviceId,
        clusterName: queryInfo.clusterName
      })
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      if (res?.data.points) {
        let data = res.data.points
        clusterPointData.value[queryInfo.clusterName] = []
        data.sort(sortFn)
        clusterPointData.value[queryInfo.clusterName] = data
        return {
          cell: 0,
          [queryInfo.clusterName]: data.map((item) => {
            return {
              ...item,
              id: item.point_id,
              title: item.point_name,
              leaf: true,
              device_id: queryInfo.deviceId,
              clusterName: queryInfo.clusterName,
              device_class: 'BMSDevice',
              device_name: queryInfo.device_name,
              packName: null,
              pointName: null
            }
          })
        }
      } else {
        let data = res.data.packages
        clusterPointData.value[queryInfo.clusterName] = {}
        let clusterIndex = queryInfo.clusterName.split('#')[0]
        data.forEach((item) => {
          clusterPointData.value[queryInfo.clusterName][item.package_name] = {}
          let packageIndex = item.package_name.split('#')[0]
          item.points.forEach((point) => {
            let pointIndex = point.point_name.split('#')[0]
            let cellVoltageIndex =
              packageIndex == 1 && clusterIndex == 1
                ? pointIndex
                : pointIndex -
                ((packageIndex - 1) * 52 + (clusterIndex - 1) * 5 * 52)
            let cellTemperatureIndex =
              packageIndex == 1 && clusterIndex == 1
                ? pointIndex
                : pointIndex -
                ((packageIndex - 1) * 20 + (clusterIndex - 1) * 5 * 20)
            if (point.unit == 'mV') {
              let voltage =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellVoltageIndex]?.voltage
              let temperature =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellVoltageIndex]?.temperature
              let VPoint_id =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellVoltageIndex]?.VPoint_id
              let TPoint_id =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellVoltageIndex]?.TPoint_id
              clusterPointData.value[queryInfo.clusterName][item.package_name][
                cellVoltageIndex
              ] = {
                voltage: voltage
                  ? voltage
                  : point.unit == 'mV'
                    ? point.value
                    : '',
                temperature: temperature
                  ? temperature
                  : point.unit == '℃'
                    ? point.value
                    : '',
                VPoint_id: VPoint_id
                  ? VPoint_id
                  : point.unit == 'mV'
                    ? point.point_id
                    : '',
                TPoint_id: TPoint_id
                  ? TPoint_id
                  : point.unit == '℃'
                    ? point.point_id
                    : ''
              }
            } else {
              let voltage =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellTemperatureIndex]?.voltage
              let temperature =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellTemperatureIndex]?.temperature
              let VPoint_id =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellTemperatureIndex]?.VPoint_id
              let TPoint_id =
                clusterPointData.value[queryInfo.clusterName][
                  item.package_name
                ][cellTemperatureIndex]?.TPoint_id
              clusterPointData.value[queryInfo.clusterName][item.package_name][
                cellTemperatureIndex
              ] = {
                voltage: voltage
                  ? voltage
                  : point.unit == 'mV'
                    ? point.value
                    : '',
                temperature: temperature
                  ? temperature
                  : point.unit == '℃'
                    ? point.value
                    : '',
                VPoint_id: VPoint_id
                  ? VPoint_id
                  : point.unit == 'mV'
                    ? point.point_id
                    : '',
                TPoint_id: TPoint_id
                  ? TPoint_id
                  : point.unit == '℃'
                    ? point.point_id
                    : ''
              }
            }
          })
        })
        return {
          cell: 1,
          [queryInfo.clusterName]: Object.keys(
            clusterPointData.value[queryInfo.clusterName]
          ).map((item) => {
            return {
              id: item,
              title: item,
              cluster: 2,
              device_class: 'BMSDevice',
              children: Object.keys(
                clusterPointData.value[queryInfo.clusterName][item]
              ).map((point) => {
                return {
                  id: `${queryInfo.clusterName}_${item}_${point}`,
                  title: `第${point}个电芯`,
                  cluster: 3,
                  device_class: 'BMSDevice',
                  children: [
                    {
                      id: clusterPointData.value[queryInfo.clusterName][item][
                        point
                      ].VPoint_id,
                      title: i18n.global.t('电压'),
                      leaf: true,
                      device_id: queryInfo.deviceId,
                      clusterName: queryInfo.clusterName,
                      packName: item,
                      pointName: `第${point}个电芯`,
                      device_class: 'BMSDevice',
                      device_name: queryInfo.device_name,
                      unit: 'V'
                    },
                    {
                      id: clusterPointData.value[queryInfo.clusterName][item][
                        point
                      ].TPoint_id,
                      title: i18n.global.t('温度'),
                      leaf: true,
                      device_id: queryInfo.deviceId,
                      clusterName: queryInfo.clusterName,
                      packName: item,
                      pointName: `第${point}个电芯`,
                      device_class: 'BMSDevice',
                      device_name: queryInfo.device_name,
                      unit: '℃'
                    }
                  ]
                }
              })
            }
          })
        }
      }
    }
    const getBatteryPointDataFn = async (queryInfo) => {
      let batteryPointData = {}
      const res = await batteryData({
        ...dataQueryInfo.value,
        ...queryInfo
      })
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      if (!res.data || !res.data.length)
        return {
          noBt: 2,
          pointId: queryInfo.pointId,
          times: [],
          datas: [],
          tableData: []
        }
      let data = res.data
      batteryPointData.times = data.map((item) => item.record_time)
      batteryPointData.datas = data.map((item) => item.value)
      batteryPointData.tableData = [
        {
          title: '',
          max: maxBy(data, 'value').value,
          maxTime: maxBy(data, 'value').record_time,
          min: minBy(data, 'value').value,
          minTime: minBy(data, 'value').record_time
        }
      ]

      return {
        noBt: 2,
        pointId: queryInfo.pointId,
        ...batteryPointData
      }
    }
    const analyseExportFn = async (queryInfo) => {
      const res = await analyseExport({
        deviceId: queryInfo.deviceId,
        date: queryInfo.date,
        clusterId: queryInfo.clusterId
      })
      if (!(res instanceof Blob) && res?.code !== 200) {
        useGlobalStore().snackbar = true
        useGlobalStore().snackbarText = res.msg
        return new Error(res.msg)
      }
      handleExport(res, `${queryInfo.deviceName}_${queryInfo.date}_数据分析报表`)
    }

    return {
      powerData,
      powerQueryInfo,
      getPowerDataFn,
      exportDataFn,
      treePointData,
      pointData,
      dataQueryInfo,
      getTreePointDataFn,
      getPointDataFn,
      clusterData,
      clusterPointData,
      batteryClusterFn,
      batteryDataFn,
      getBatteryPointDataFn,
      analyseExportFn
    }
  }
  // {
  //   persist: true
  // }
)
