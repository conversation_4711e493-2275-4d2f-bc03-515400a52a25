<script setup>
import { ref, toRefs, getCurrentInstance } from 'vue'
import { useDeviceConfigStore } from '@/store/module/deviceConfig'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import {
  backupDeviceTable,
  forceDeviceTable,
  restoreDeviceTable,
  deleteFile
} from '@/api/deviceConfig'
import { VAceEditor } from 'vue3-ace-editor'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/module/user'
import vkbeautify from 'vkbeautify'
import { rebootEMS, rebootEMSs } from '@/api/deviceConfig'

const { userInfo } = toRefs(useUserStore())
const router = useRouter()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { isEdit, filesData, queryInfo } = toRefs(useDeviceConfigStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const headers = ref([
  { title: '#', key: 'id', sortable: false },
  { title: t('文件名称'), key: 'fileName', sortable: false },
  { title: t('操作'), key: 'action', sortable: false }
])

const getData = () => {
  useDeviceConfigStore().getFilesData()
}
getData()

// 查看
const lookDialog = ref(false)
const lookData = ref()
const handleLookClick = async (item) => {
  queryInfo.value.fileName = item.fileName
  const res = await useDeviceConfigStore().getFileInfoData()
  lookDialog.value = true
  lookData.value = vkbeautify.xml(res)
}

// 修改
const handleEditClick = async (item) => {
  queryInfo.value.fileName = item.fileName
  const res = await useDeviceConfigStore().getFileInfoData()
  isEdit.value = true
  router.push('/deviceConfig')
}

// 备份
const systemDialog = ref(false)
const handleBackUpClick = async (item) => {
  file.value = undefined
  queryInfo.value.fileName = item.fileName
  fileDialog.value = true
}
const handleRestoreBackUpClick = async (item) => {
  password.value = undefined
  queryInfo.value.fileName = item.fileName
  const res = await restoreDeviceTable(
    JSON.stringify({
      fileName: item.fileName
    })
  )
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  pwdDialog.value = true
}
const handleSystemConfirm = async () => {
  const res = await forceDeviceTable({
    fileName: queryInfo.value.fileName
  })
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  getData()
  snackbar.value = true
  snackbarText.value = t('备份成功')
}
const handleDeleteClick = async (item) => {
  const res = await deleteFile({
    fileName: item.fileName
  })
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  getData()
  snackbar.value = true
  snackbarText.value = t('删除成功')
}
/**
 * 密码弹框
 */
const pwdDialog = ref(false)
const password = ref()
const eye = ref(false)
const handleEyeClick = () => (eye.value = !eye.value)
const handlePwdCancelClick = () => {
  pwdDialog.value = false
}
const handlePwdConfirm = async () => {
  if (!password.value) {
    snackbar.value = true
    snackbarText.value = t('请输入密码')
    return
  }
  const res = await rebootEMS(JSON.stringify({ password: password.value }))
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  const res2 = await rebootEMSs({ name: res.data })
  if (res2.code !== 201) {
    snackbar.value = true
    snackbarText.value = res2.msg
    return
  }
  pwdDialog.value = false
  snackbar.value = true
  snackbarText.value = t('恢复备份成功')
  getData()
}
/**
 * 备份文件名
 */
const fileDialog = ref(false)
const file = ref()
const handleFileConfirm = async () => {
  if (!file.value) {
    snackbar.value = true
    snackbarText.value = t('请输入文件名')
    return
  }
  const res = await backupDeviceTable(
    JSON.stringify({
      fileName: file.value
    })
  )
  if (res.code == 409) {
    fileDialog.value = false
    systemDialog.value = true
    return
  }
  if (res.code !== 201) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  fileDialog.value = false
  getData()
  snackbar.value = true
  snackbarText.value = t('备份成功')
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const keyboardProp = ref()
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  keyboardProp.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  if (keyboardProp.value == 'password') {
    password.value = keyboardInputValue.value
  } else {
    file.value = keyboardInputValue.value
  }
  showKeyboard.value = false
  keyboardDialog.value = false
}

defineExpose({
  getData
})
</script>

<template>
  <div class="w-100 px-2" v-if="!isEdit">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <v-data-table
        :headers="headers"
        :items="filesData"
        :hide-default-footer="true"
      >
        <template v-slot:item.action="{ item }">
          <v-btn @click="handleLookClick(item)">{{ $t('查看') }}</v-btn>
          <v-btn
            class="ml-2"
            @click="handleEditClick(item)"
            v-if="
              userInfo.permission_level == 4 ||
              item.fileName == 'devices_table.xml'
            "
            >{{ $t('修改') }}</v-btn
          >
          <v-btn
            class="ml-2"
            @click="handleBackUpClick(item)"
            v-if="item.fileName == 'devices_table.xml'"
            >{{ $t('备份') }}</v-btn
          >
          <v-btn
            class="mx-2"
            @click="handleRestoreBackUpClick(item)"
            v-if="item.fileName != 'devices_table.xml'"
            >{{ $t('恢复备份') }}</v-btn
          >
          <v-btn
            @click="handleDeleteClick(item)"
            v-if="item.fileName != 'devices_table.xml'"
            >{{ $t('删除') }}</v-btn
          >
        </template>
      </v-data-table>

      <v-dialog v-model="lookDialog" width="auto">
        <v-card width="1000" class="pa-4 rounded-lg">
          <v-ace-editor
            v-model:value="lookData"
            lang="html"
            theme="monokai"
            style="height: 600px"
            :options="{
              useWorker: true,
              enableBasicAutocompletion: true,
              enableSnippets: true,
              enableLiveAutocompletion: true
            }"
          />
        </v-card>
      </v-dialog>
      <v-dialog v-model="systemDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text>{{ $t('是否覆盖已有文件？') }}</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="systemDialog = false"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handleSystemConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="pwdDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text class="my-0">
            <div class="mb-2">{{ $t('请输入密码') }}</div>
            <v-text-field
              v-model="password"
              :rules="[(v) => !!v || $t('密码必填')]"
              variant="outlined"
              class="mb-2"
              :type="eye ? 'text' : 'password'"
              :append-inner-icon="eye ? 'mdi-eye' : 'mdi-eye-closed'"
              @click:appendInner.stop="handleEyeClick()"
              @click:control="handleShow($event, password, 'password')"
              hide-details
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handlePwdCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handlePwdConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="fileDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text class="my-0">
            <div class="mb-2">{{ $t('请输入所要备份的文件名') }}</div>
            <v-text-field
              v-model="file"
              :rules="[(v) => !!v || $t('必填')]"
              variant="outlined"
              class="mb-2"
              @click:control="handleShow($event, file, 'file')"
              hide-details
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="fileDialog = false"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handleFileConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </div>
  <template v-else>
    <router-view />
  </template>
</template>

<style lang="scss" scoped></style>
