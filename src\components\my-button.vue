<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-22 15:17:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-22 15:17:53
 * @FilePath: \ems_manage\src\components\my-button.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="width: 100%; height: 100%">
    <v-btn :color="props.bgColor">
      {{ props.text }}
    </v-btn>
  </div>
</template>
<script setup>
const props = defineProps({
  text: String,
  bgColor: String,
  fontFamily: String
})
</script>
<style scoped></style>
