<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:27:14
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 10:30:39
 * @FilePath: \ems_manage\src\views\Login\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, getCurrentInstance, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/module/user'
import { useI18n } from 'vue-i18n'
import { useLocale } from 'vuetify'
import { initLocale } from '@/locale'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'

import bottom from '@/assets/img/bottom.png'
import tree1 from '@/assets/img/tree1.png'
import tree2 from '@/assets/img/tree2.png'
import LangSelect from '@/components/lang-select'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const router = useRouter()
const user = useUserStore()
const { systemInfo } = toRefs(useConfigStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())

const form = ref({
  user_name: '',
  password: ''
})
const usernameRules = ref([(v) => !!v || t('账号必填')])
const passwordRules = ref([(v) => !!v || t('密码必填')])

const isRemember = ref(false)
isRemember.value = JSON.parse(localStorage.getItem('isRemember'))
if (isRemember.value) {
  form.value.user_name = localStorage.getItem('username')
  form.value.password = localStorage.getItem('password')
}

const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.loginForm.validate()
  if (!valid) return
  loading.value = true
  try {
    const result = await user.loginFn({
      user_name: form.value.user_name,
      password: form.value.password
    })
    if (result?.msg) {
      snackbar.value = true
      snackbarText.value = result.msg
      loading.value = false
      return
    }
    if (isRemember.value) {
      localStorage.setItem('username', form.value.user_name)
      localStorage.setItem('password', form.value.password)
      localStorage.setItem('isRemember', JSON.stringify(isRemember.value))
    }

    loading.value = false
    router.push('/')
    showKeyboard.value = false
    currentInput.value = null
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    showKeyboard.value = false
    currentInput.value = null
  }
}

const handleVisitorLogin = async () => {
  try {
    const result = await user.loginFn({
      user_name: 'observer',
      password: 'comeon_8Getit'
    })
    if (result?.msg) {
      snackbar.value = true
      snackbarText.value = result.msg
      loading.value = false
      return
    }
    loading.value = false
    router.push('/')
    showKeyboard.value = false
    currentInput.value = null
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    showKeyboard.value = false
    currentInput.value = null
  }
}

const eye = ref(false)
const handleEyeClick = () => (eye.value = !eye.value)

/**
 * 语言
 */
const { current } = useLocale()
const langArr = ref([
  {
    value: 'zhHans',
    title: '简体中文'
  },
  {
    value: 'en',
    title: 'English'
  }
])
const langTitle = ref()
langTitle.value = langArr.value.find(
  (item) => item.value == current.value
).title
const handleChangeLang = (lang) => {
  langTitle.value = langArr.value.find((item) => item.value == lang).title
  useGlobalStore()
    .languageSetFn({ language: lang })
    .then(() => {
      localStorage.setItem('lang', lang)
      current.value = lang
      initLocale(lang)
      router.go(0)
    })
}

/**
 * logo
 */
const logo = ref()
const getData = () => {
  useConfigStore()
    .getSystemInfoFn()
    .then((res) => {
      logo.value =
        'data:image/png;base64,' +
        decodeURIComponent(decodeURIComponent(systemInfo.value.logo))
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

/**
 * 键盘
 */
keyboardMode.value = 'cn'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
const keyboardArr = ref([
  {
    value: false,
    title: t('显示键盘')
  },
  {
    value: true,
    title: t('隐藏键盘')
  }
])
const handleKeyboardLang = (value) => {
  isShowKeyboard.value = value
  if (!isShowKeyboard.value) {
    snackbarText.value = t('键盘已开启')
  } else {
    snackbarText.value = t('键盘已关闭')
  }
  snackbar.value = true
}
</script>

<template>
  <v-main class="d-flex justify-center align-center">
    <div
      style="background-color: #f4f5fa; position: relative"
      class="w-full h-full d-flex justify-center align-center"
    >
      <div
        style="position: absolute; top: 20px"
        class="w-100 flex justify-space-between px-8"
      >
        <div>
          <img
            :src="logo"
            alt="logo"
            style="width: 180px"
            v-if="systemInfo.logo"
          />
        </div>
        <div>
          <!-- <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn
                append-icon="mdi-translate"
                size="large"
                variant="plain"
                v-bind="props"
                >{{ langTitle }}</v-btn
              >
            </template>

            <v-list>
              <v-list-item
                v-for="item in langArr"
                :key="item.value"
                class="cursor-pointer"
              >
                <v-list-item-title @click="handleChangeLang(item.value)">{{
                  item.title
                }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu> -->
          <lang-select />
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn
                append-icon="mdi-keyboard"
                size="large"
                variant="plain"
                v-bind="props"
                >{{ $t('键盘') }}</v-btn
              >
            </template>

            <v-list>
              <v-list-item
                v-for="item in keyboardArr"
                :key="item.title"
                class="cursor-pointer"
              >
                <v-list-item-title @click="handleKeyboardLang(item.value)">{{
                  item.title
                }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>

      <v-card
        elevation="6"
        class="px-5 py-8"
        style="border-radius: 24px; z-index: 1"
      >
        <v-card-title class="text-h4 text-center">EMS</v-card-title>
        <v-sheet class="mx-auto" width="480">
          <v-form fast-fail @submit.prevent="submit" ref="loginForm">
            <v-text-field
              v-model="form.user_name"
              :rules="usernameRules"
              :label="$t('账号')"
              :clearable="true"
              variant="outlined"
              class="mb-2"
              @click:control="handleShow($event, form.user_name, 'user_name')"
            ></v-text-field>

            <v-text-field
              v-model="form.password"
              :rules="passwordRules"
              :label="$t('密码')"
              :clearable="true"
              variant="outlined"
              :type="eye ? 'text' : 'password'"
              :append-inner-icon="eye ? 'mdi-eye' : 'mdi-eye-closed'"
              @click:appendInner="handleEyeClick()"
              @click:control="handleShow($event, form.password, 'password')"
            ></v-text-field>

            <div class="flex justify-between align-center">
              <v-checkbox
                v-model="isRemember"
                :label="$t('记住密码')"
                color="primary"
                hide-details
              ></v-checkbox>
              <!-- <v-btn variant="text" @click="handleVisitorLogin">
                {{ $t('游客登录') }}
              </v-btn> -->
            </div>

            <v-btn
              class="mt-2"
              type="submit"
              block
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('登录') }}</v-btn
            >
          </v-form>
        </v-sheet>
      </v-card>

      <div style="position: fixed; bottom: -10px" class="w-100">
        <div style="position: relative; height: 100px">
          <img
            :src="tree1"
            style="position: absolute; right: 15%; bottom: -50%; width: 130px"
          />

          <img
            :src="tree2"
            style="position: absolute; left: 10%; bottom: -80%; width: 130px"
          />
        </div>

        <!-- bg img -->
        <img class="w-100" :src="bottom" />
      </div>
    </div>
  </v-main>
</template>

<style lang="scss" scoped></style>
