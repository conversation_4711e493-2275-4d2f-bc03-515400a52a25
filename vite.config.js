/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:15:54
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 12:22:31
 * @FilePath: \ems_manage\vite.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import UnoCSS from 'unocss/vite'
import legacy from '@vitejs/plugin-legacy'

import { visualizer } from 'rollup-plugin-visualizer'
import { Plugin as importToCDN } from "vite-plugin-cdn-import";
import viteCompression from 'vite-plugin-compression';
import ElementPlus from 'unplugin-element-plus/vite'

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    vue(),
    UnoCSS(),
    // 打包分析
    visualizer({ open: true }),
    // 低版本浏览器兼容
    // legacy({
    //   targets: ['ie >= 11', 'chrome < 70'],
    //   additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    //   renderLegacyChunks: true,
    //   polyfills: [
    //     'es.symbol',
    //     'es.array.filter',
    //     'es.promise',
    //     'es.promise.finally',
    //     'es/map',
    //     'es/set',
    //     'es.array.for-each',
    //     'es.object.define-properties',
    //     'es.object.define-property',
    //     'es.object.get-own-property-descriptor',
    //     'es.object.get-own-property-descriptors',
    //     'es.object.keys',
    //     'es.object.to-string',
    //     'web.dom-collections.for-each',
    //     'esnext.global-this',
    //     'esnext.string.match-all'
    //   ]
    // }),
    // CDN引入
    // importToCDN({
    //   //（prodUrl解释： name: 对应下面modules的name，version: 自动读取本地package.json中dependencies依赖中对应包的版本号，path: 对应下面modules的path，当然也可写完整路径，会替换prodUrl）
    //   prodUrl: "https://cdn.bootcdn.net/ajax/libs/{name}/{version}/{path}",
    //   modules: [
    //     "dayjs",
    //     {
    //       name: 'axios',
    //       var: 'axios',
    //       path: 'https://cdn.jsdelivr.net/npm/axios@1.7.4/dist/axios.min.js'
    //     },
    //     {
    //       name: "echarts",
    //       var: "echarts",
    //       path: "echarts.min.js"
    //     }
    //   ],
    // }),
    // 开启gzip
    viteCompression({
      threshold: 0,
      deleteOriginFile: false,
      filter: () => true,
      ext: ".gz",
    }),
    ElementPlus()
  ],
  resolve: {
    // https://cn.vitejs.dev/config/#resolve-alias
    // 配置别名
    alias: {
      // 设置路径
      '~': path.resolve(__dirname, './'),
      // 设置别名
      '@': path.resolve(__dirname, './src')
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  // 代理配置
  server: {
    host: '0.0.0.0',
    proxy: {
      // https://cn.vitejs.dev/config/#server-proxy
      '/api': {
        // target: 'http://localhost:8081',
        target: 'http://*************/api',
        // target: 'http://*************:8081/',
        // target: 'https://pd308.elecod-cloud.com/api',
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/api/, '')
      },
      '/ws': {
        target: 'ws://*************:8080/api',
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/ws/, '')
      }
    },
    hmr: {
      overlay: false
    },
    strictPort: true,
    port: 8082
  },
  build: {
    // https://cn.vitejs.dev/guide/build.html#browser-compatibility
    sourcemap: false,
    // 消除打包大小超过500kb警告
    chunkSizeWarningLimit: 4000,
    target: 'es2020',
  },
  // esbuild: {
  //   supported: {
  //     bigint: true
  //   }
  // },
  define: {
    'process.env.NODE_ENV': '"production"',
  },
  optimizeDeps: {
    include: [
      "dayjs",
      "axios",
      "pinia",
      "vue-i18n",
      "vuetify"
    ],
    esbuildOptions: {
      target: "es2020"
    }
  }
})
