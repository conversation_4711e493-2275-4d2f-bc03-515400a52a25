<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-04-24 12:13:53
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 10:27:03
 * @FilePath: \ems_manage\src\views\System\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, computed, toRefs, getCurrentInstance } from 'vue'
import { useDisplay, useLocale } from 'vuetify'
import { useUserStore } from '@/store/module/user'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'
import { initLocale } from '@/locale'

import { LangSelectVersion } from '@/components/lang-select'

const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const router = useRouter()
const { userInfo, menuInfo } = toRefs(useUserStore())
const { version, cycleTime, timeZoneList, localTimeZone } = toRefs(
  useConfigStore()
)

useConfigStore()
  .getVersionInfoFn()
  .catch((err) => {
    snackbar.value = true
    snackbarText.value = err
  })

/**
 * 语言
 */
const { current } = useLocale()
const langOptions = ref([
  {
    text: '中文',
    value: 'zhHans'
  },
  {
    text: 'English',
    value: 'en'
  }
])
const handleChangeLang = () => {
  useGlobalStore()
    .languageSetFn({ language: current.value })
    .then(() => {
      localStorage.setItem('lang', current.value)
      initLocale(current.value)
      router.go(0)
    })
}

/**
 * 键盘
 */
const handleShowKeyboard = () => {
  if (!isShowKeyboard.value) {
    snackbarText.value = t('键盘已开启')
  } else {
    snackbarText.value = t('键盘已关闭')
  }
  snackbar.value = true
}
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  cycleTime.value = keyboardInputValue.value
  keyboardDialog.value = false
}

/**
 * 时区
 */
const timeZone = ref()
useConfigStore()
  .getTimeZoneListFn()
  .then(() => {
    timeZone.value = localTimeZone.value
  })
const handleTimeConfirm = () => {
  useConfigStore()
    .setTimeZoneFn({
      timezone: timeZone.value
    })
    .then(() => {
      snackbar.value = true
      snackbarText.value = t('设置成功')
      timeZone.value = localTimeZone.value
    })
}
</script>

<template>
  <div class="pa-6 h-100 overflow-hidden d-flex w-100">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4 overflow-auto no-scrollbar"
      elevation="4"
    >
      <div class="d-flex justify-between align-center py-2 mb-4">
        <div class="text-h6">{{ $t('系统信息') }}</div>
      </div>
      <v-card-text class="flex justify-center align-center h-50">
        <div>
          <div class="text-subtitle-1 mb-3">
            {{ $t('软件版本') }}：<span class="color-primary">{{
              version
            }}</span>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('系统语言') }}：
            <div class="cont">
              <!-- <v-radio-group
                v-model="current"
                inline
                @update:modelValue="handleChangeLang"
                hide-details
              >
                <v-radio
                  :label="item.text"
                  :value="item.value"
                  v-for="item in langOptions"
                  :key="item.value"
                ></v-radio>
              </v-radio-group> -->
              <lang-select-version />
            </div>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('系统键盘') }}：
            <div class="cont">
              <v-radio-group
                v-model="isShowKeyboard"
                inline
                @update:modelValue="handleShowKeyboard"
                hide-details
              >
                <v-radio :label="$t('开启')" :value="false"></v-radio>
                <v-radio :label="$t('关闭')" :value="true"></v-radio>
              </v-radio-group>
            </div>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('循环时长') }}：
            <div class="cont">
              <v-text-field
                v-model="cycleTime"
                variant="outlined"
                label=""
                hide-details
                @click:control="handleShow($event, cycleTime)"
              ></v-text-field>
            </div>
            &nbsp; ms
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('设置时区') }}：
            <div class="cont">
              <v-autocomplete
                v-model="timeZone"
                item-value="value"
                clearable
                :items="timeZoneList"
                variant="outlined"
                hide-details
              ></v-autocomplete>
            </div>
            <v-btn @click="handleTimeConfirm">{{ $t('确定') }}</v-btn>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<style lang="scss" scoped>
.cont {
  width: 250px;
  margin-right: 10px;
}
</style>
