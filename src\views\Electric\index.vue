<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-07 11:32:14
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-13 17:44:45
 * @FilePath: \ems_manage\src\views\Electric\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, watch, toRefs, nextTick, computed } from 'vue'
import dayjs from '@/utils/date'
import { useStatisticsStore } from '@/store/module/statistics'
import { useDeviceStore } from '@/store/module/device'
import { isEmpty } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'
import { clearEnergy } from '@/api/statistics'
import { useUserStore } from '@/store/module/user'
import { useAllowedDate } from '@/hook/useAllowedDate'
import { setLeftWidth } from '@/utils'

import BarEchart from './barEchart.vue'
import { ElDatePicker } from 'element-plus'

const { allowedDates } = useAllowedDate()
const { userInfo } = toRefs(useUserStore())
const { t } = useI18n()
const { treeData } = toRefs(useDeviceStore())
const { powerData, powerQueryInfo } = toRefs(useStatisticsStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const deviceList = computed(() => {
  return treeData.value.filter((item) => item.is_power_accumulation == 1)
})
const deviceId = ref()
const handleDeviceChange = (e) => {
  if (!e) return
  powerQueryInfo.value.deviceId = e
  useStatisticsStore().getPowerDataFn({
    date: dayjs(e).format('YYYY-MM-DD'),
    type: toggle.value
  })
}
useDeviceStore()
  .getDeviceListFn()
  .then(() => {
    if (deviceList.value.length) {
      deviceId.value = deviceList.value[0].id
      powerQueryInfo.value.deviceId = deviceList.value[0].id
      useStatisticsStore().getPowerDataFn({
        type: toggle.value
      })
    }
  })

const date = ref([])
const month = ref()
const year = ref()
const formatDate = ref()
const isShowDate = ref(false)
date.value = new Date()
formatDate.value = dayjs(date.value).format('YYYY-MM-DD')
powerQueryInfo.value.date = formatDate.value
const handleDateChange = (e) => {
  if (viewMode.value !== 'month') return
  formatDate.value = dayjs(e).format('YYYY-MM-DD')
  isShowDate.value = false
  powerQueryInfo.value.date = dayjs(e).format('YYYY-MM-DD')
  useStatisticsStore().getPowerDataFn({
    type: 0
  })
}
const handleMonthChange = (e) => {
  let value = e + 1
  if (viewMode.value !== 'months') return
  formatDate.value = `${year.value}-${value < 10 ? '0' + value : value}`
  isShowDate.value = false
  powerQueryInfo.value.date = formatDate.value + '-01'
  useStatisticsStore().getPowerDataFn({
    type: 1
  })
}
const handleYearChange = (e) => {
  if (viewMode.value !== 'year') return
  formatDate.value = e
  isShowDate.value = false
  powerQueryInfo.value.date = formatDate.value + '-01' + '-01'
  useStatisticsStore().getPowerDataFn({
    type: 2
  })
}

const toggle = ref(0)
const viewMode = ref('month')
watch(toggle, (newValue, oldValue) => {
  if (newValue == undefined) return
  date.value = new Date()
  let initArr = headers.value.slice(0, 2)
  if (toggle.value == 0) {
    viewMode.value = 'month'
    formatDate.value = dayjs(date.value).format('YYYY-MM-DD')
    headers.value = [...initArr, ...mapHeader(0)]
  } else if (toggle.value == 1) {
    viewMode.value = 'months'
    formatDate.value = dayjs(date.value).format('YYYY-MM')
    month.value = Number(dayjs(date.value).format('MM')) - 1
    year.value = Number(dayjs(date.value).format('YYYY'))
    headers.value = [...initArr, ...mapHeader(1)]
  } else if (toggle.value == 2) {
    viewMode.value = 'year'
    formatDate.value = dayjs(date.value).format('YYYY')
    year.value = Number(dayjs(date.value).format('YYYY'))
    headers.value = [...initArr, ...mapHeader(2)]
  }
  powerQueryInfo.value.date = dayjs(date.value).format('YYYY-MM-DD')
  useStatisticsStore().getPowerDataFn({
    type: toggle.value
  })
})

const headers = ref([
  {
    title: t('数据名称'),
    align: 'center',
    key: 'name',
    minWidth: '150px',
    fixed: true
  },
  {
    title: `${t('总电量')}(kWh)`,
    align: 'center',
    key: 'sumData',
    minWidth: '150px'
  }
])

const mapHeader = (type) => {
  let arr = []
  if (type == 0) {
    for (let i = 0; i <= 23; i++) {
      arr.push({
        prop: i + '',
        title: t(`${i < 10 ? '0' + i : i}时`),
        align: 'center',
        key: `hour_${i}`,
        minWidth: '100px'
      })
    }
  } else if (type == 1) {
    for (let i = 1; i <= 31; i++) {
      arr.push({
        prop: i + '',
        title: t(`${i < 10 ? '0' + i : i}日`),
        align: 'center',
        key: `day_${i}`,
        minWidth: '100px'
      })
    }
  } else if (type == 2) {
    for (let i = 1; i <= 12; i++) {
      arr.push({
        prop: i + '',
        title: t(`${i}月`),
        align: 'center',
        key: `month_${i}`,
        minWidth: '100px'
      })
    }
  }
  return arr
}
headers.value = [...headers.value, ...mapHeader(0)]

/**
 * 导出
 */
const dialog = ref(false)
const form = ref({})
const handleExportClick = () => {
  let nowDate = dayjs(new Date())
  form.value = {
    flag: 'day',
    deviceId: undefined,
    start: nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
    end: nowDate.format('YYYY-MM-DD'),
    formatDate: [
      nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
      nowDate.format('YYYY-MM-DD')
    ],
    formatYear: [nowDate.format('YYYY'), nowDate.format('YYYY')],
    formatMonth: [nowDate.format('YYYY-MM'), nowDate.format('YYYY-MM')]
  }
  dialog.value = true
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}
const handleCancelDiaClick = () => {
  let nowDate = dayjs(new Date())
  form.value = {
    flag: 'day',
    deviceId: undefined,
    start: nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
    end: nowDate.format('YYYY-MM-DD'),
    formatDate: [
      nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
      nowDate.format('YYYY-MM-DD')
    ],
    formatYear: [nowDate.format('YYYY'), nowDate.format('YYYY')],
    formatMonth: [nowDate.format('YYYY-MM'), nowDate.format('YYYY-MM')]
  }
  dialog.value = false
}
const loading = ref(false)
const handleConfirmDiaClick = () => {
  if (!form.value.deviceId) {
    snackbar.value = true
    snackbarText.value = t('请选择设备')
    return
  }
  if (!form.value.start && !form.value.end) {
    snackbar.value = true
    snackbarText.value = t('请选择日期')
    return
  }
  loading.value = true
  let value = deviceList.value.find((item) => item.id == form.value.deviceId)
  useStatisticsStore()
    .exportDataFn({
      deviceId: form.value.deviceId,
      start: form.value.start,
      end: form.value.end,
      deviceName: value.title,
      flag: form.value.flag
    })
    .then((res) => {
      loading.value = false
      dialog.value = false
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
const handleDayExportChange = (e) => {
  if (!e) return
  form.value.start = dayjs(e[0]).format('YYYY-MM-DD')
  form.value.end = dayjs(e[1]).format('YYYY-MM-DD')
}
const handleMonthExportChange = (e) => {
  if (!e) return
  form.value.start = dayjs(e[0]).startOf('month').format('YYYY-MM-DD')
  form.value.end = dayjs(e[1]).endOf('month').format('YYYY-MM-DD')
}
const handleYearExportChange = (e) => {
  if (!e) return
  form.value.start = e[0]
  form.value.end = e[1]
}
watch(
  () => form.value.flag,
  () => {
    let nowDate = dayjs(new Date())
    if (form.value.flag == 'day') {
      form.value.start = nowDate.subtract(7, 'day').format('YYYY-MM-DD')
      form.value.end = nowDate.format('YYYY-MM-DD')
      form.value.formatDate = [
        nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
        nowDate.format('YYYY-MM-DD')
      ]
    } else if (form.value.flag == 'month') {
      form.value.start = nowDate.startOf('month').format('YYYY-MM-DD')
      form.value.end = nowDate.endOf('month').format('YYYY-MM-DD')
      form.value.formatMonth = [
        nowDate.format('YYYY-MM'),
        nowDate.format('YYYY-MM')
      ]
    } else if (form.value.flag == 'year') {
      form.value.start = nowDate.format('YYYY')
      form.value.end = nowDate.format('YYYY')
      form.value.formatYear = [nowDate.format('YYYY'), nowDate.format('YYYY')]
    }
  }
)
const shortcuts = [
  {
    text: t('最近一周'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('最近一个月'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]
const shortcutsYear = [
  {
    text: t('今年'),
    value: [new Date(), new Date()]
  },
  {
    text: t('去年'),
    value: () => {
      const end = new Date()
      const start = new Date(
        new Date().setFullYear(new Date().getFullYear() - 10)
      )
      return [start, end]
    }
  }
]
const shortcutsMonth = [
  {
    text: t('本月'),
    value: [new Date(), new Date()]
  },
  {
    text: t('最近三个月'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    }
  }
]
const disabledDate = (date) => {
  return date.getTime() > new Date().getTime()
}

/**
 * 清除电量
 */
const handleClearClick = () => {
  password.value = ''
  clearDialog.value = true
}
const clearDialog = ref(false)
const password = ref()
const handleCancelClearClick = () => {
  password.value = ''
  clearDialog.value = false
}
const eye = ref(false)
const handleEyeClick = () => (eye.value = !eye.value)
const handleConfirmClearClick = () => {
  if (!password.value) {
    snackbar.value = true
    snackbarText.value = t('请输入密码')
    return
  }
  loading.value = true
  useUserStore()
    .loginFn({
      user_name: userInfo.value.user_name,
      password: password.value
    })
    .then((result) => {
      if (result?.msg) {
        snackbar.value = true
        snackbarText.value = result.msg
        loading.value = false
        return
      }
      if (userInfo.value.permission_level == 2) {
        snackbar.value = true
        snackbarText.value = t('您的权限不足')
        loading.value = false
      } else {
        clearEnergy().then((res) => {
          if (res.code !== 200) {
            loading.value = false
            snackbar.value = true
            snackbarText.value = res.msg
            return
          }
          loading.value = false
          snackbar.value = true
          snackbarText.value = t('清除成功')
          clearDialog.value = false
        })
      }
    })
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  password.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
</script>

<template>
  <div class="pa-6 h-100 w-100 overflow-hidden d-flex">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4ov overflow-auto no-scrollbar"
      elevation="4"
    >
      <div class="d-flex justify-between align-center py-2 mb-4">
        <div class="text-h6">{{ $t('电量统计') }}</div>
        <div class="d-flex align-center">
          <v-select
            v-model="deviceId"
            item-value="id"
            clearable
            :label="$t('选择设备')"
            :items="deviceList"
            variant="outlined"
            class="w-200px mr-4"
            @update:modelValue="handleDeviceChange"
            hide-details
          ></v-select>
          <v-btn-toggle
            v-model="toggle"
            variant="outlined"
            divided
            class="mr-4"
          >
            <v-btn>{{ $t('日') }}</v-btn>
            <v-btn>{{ $t('月') }}</v-btn>
            <v-btn>{{ $t('年') }}</v-btn>
          </v-btn-toggle>
          <v-menu
            v-model="isShowDate"
            location="bottom"
            :close-on-content-click="false"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-model="formatDate"
                prepend-inner-icon="mdi-calendar-range"
                title="Date"
                variant="outlined"
                hide-details
                single-line
                v-bind="props"
                style="width: 200px"
              ></v-text-field>
            </template>

            <v-date-picker
              v-model="date"
              v-model:month="month"
              v-model:year="year"
              show-adjacent-months
              :allowed-dates="allowedDates"
              color="primary"
              elevation="4"
              class="date-picker"
              :view-mode="viewMode"
              @update:modelValue="handleDateChange"
              @update:month="handleMonthChange"
              @update:year="handleYearChange"
            ></v-date-picker>
          </v-menu>
          <v-btn
            height="48px"
            class="ml-4"
            color="primary"
            @click="handleExportClick"
            >{{ $t('导出报表') }}</v-btn
          >
          <v-btn
            height="48px"
            class="ml-4"
            color="primary"
            @click="handleClearClick"
            v-if="userInfo.permission_level != 1"
            >{{ $t('清除电量') }}</v-btn
          >
        </div>
      </div>
      <template v-if="!isEmpty(powerData.chartData) || !deviceList.length">
        <v-data-table
          :headers="headers"
          :items="powerData.tableData"
          hide-default-footer
          class="px-6"
        >
        </v-data-table>
        <BarEchart style="height: 63%" class="mt-4" :type="viewMode" />
      </template>
      <empty v-else />
    </v-card>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="640" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-2">{{
          $t('导出报表')
        }}</v-card-title>
        <div class="flex">
          <div
            class="text-body-1 pb-2 pt-3 pr-4 flex justify-end align-center dia-left"
          >
            {{ $t('选择设备') }}
          </div>
          <v-col cols="9" class="pl-0 pr-2">
            <v-select
              v-model="form.deviceId"
              item-value="id"
              clearable
              :items="deviceList"
              variant="outlined"
              class="w-100 mr-4 mt-2"
              :placeholder="$t('选择设备')"
              hide-details
            ></v-select>
          </v-col>
        </div>
        <div class="flex">
          <div
            class="text-body-1 pb-2 pt-3 pr-4 flex justify-end align-center dia-left"
          >
            <div class="mr-1">{{ $t('选择类型') }}</div>
            <v-tooltip location="top">
              <template #default>
                <div>
                  <div>{{ $t('选择日：导出的是每一天的时刻统计报表') }}</div>
                  <div>{{ $t('选择月：导出的是每个月的天统计表') }}</div>
                  <div>{{ $t('选择年：导出的是每天的月份统计表') }}</div>
                </div>
              </template>
              <template v-slot:activator="{ props }">
                <v-icon
                  icon="mdi-help-circle"
                  size="small"
                  v-bind="props"
                ></v-icon> </template
            ></v-tooltip>
          </div>
          <v-col cols="9" class="pl-0 pr-2">
            <v-radio-group v-model="form.flag" inline hide-details>
              <v-radio :label="$t('日')" value="day"></v-radio>
              <v-radio :label="$t('月')" value="month"></v-radio>
              <v-radio :label="$t('年')" value="year"></v-radio>
            </v-radio-group>
          </v-col>
        </div>
        <div class="flex">
          <div
            class="text-body-1 pb-2 pt-3 pr-4 flex justify-end align-center dia-left"
          >
            {{ $t('选择日期') }}
          </div>
          <v-col cols="9" class="pl-0 pr-2">
            <el-date-picker
              v-model="form.formatDate"
              type="daterange"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('开始日期')"
              :end-placeholder="$t('结束日期')"
              :shortcuts="shortcuts"
              size="large"
              :clearable="false"
              :disabled-date="disabledDate"
              class="w-100"
              style="height: 56px"
              v-if="form.flag == 'day'"
              @change="handleDayExportChange"
            />
            <el-date-picker
              v-model="form.formatMonth"
              type="monthrange"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('开始月份')"
              :end-placeholder="$t('结束月份')"
              :shortcuts="shortcutsMonth"
              v-if="form.flag == 'month'"
              class="w-100"
              style="height: 56px"
              @change="handleMonthExportChange"
            />
            <el-date-picker
              v-model="form.formatYear"
              type="yearrange"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('开始年份')"
              :end-placeholder="$t('结束年份')"
              :shortcuts="shortcutsYear"
              v-if="form.flag == 'year'"
              class="w-100"
              style="height: 56px"
              value-format="YYYY"
              @change="handleYearExportChange"
            />
          </v-col>
        </div>
        <div class="d-flex justify-center mt-2">
          <v-btn
            class="mt-2 mr-4 px-8"
            height="50"
            @click="handleCancelDiaClick"
            >{{ $t('取消') }}</v-btn
          >
          <v-btn
            class="mt-2 px-8"
            height="50"
            :loading="loading"
            color="primary"
            @click="handleConfirmDiaClick"
            >{{ $t('确定') }}</v-btn
          >
        </div>
      </v-card>
    </v-dialog>

    <v-dialog v-model="clearDialog" width="auto">
      <v-card width="640" class="pa-4 rounded-lg">
        <v-card-title>{{ $t('系统提示') }}</v-card-title>
        <v-card-text class="my-0">
          <div class="mb-2">{{ $t('请输入密码') }}</div>
          <v-text-field
            v-model="password"
            :rules="[(v) => !!v || $t('密码必填')]"
            variant="outlined"
            class="mb-2"
            :type="eye ? 'text' : 'password'"
            :append-inner-icon="eye ? 'mdi-eye' : 'mdi-eye-closed'"
            @click:appendInner.stop="handleEyeClick()"
            @click:control="handleShow($event, password)"
            hide-details
          ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="handleCancelClearClick"
          >
            {{ $t('取消') }}
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="handleConfirmClearClick"
          >
            {{ $t('确定') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
/* :deep(.v-table) {
  border-radius: none;
  table {
    border-collapse: collapse;
  }
  td,
  th {
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  }
} */
:deep(.v-img) {
  height: 600px !important;
}
:deep(.v-empty-state) {
  padding-bottom: 150px !important;
}
:deep(.el-input__wrapper) {
  height: auto;
  padding: 16px;
  box-shadow: 0 0 0 1px rgba(118, 118, 118, 0.6);
  .el-range__icon {
    color: #000;
    font-size: 16px;
  }
  .el-range-input {
    color: #000;
    height: 26px;
    font-size: 16px;
  }
}
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px #000;
}
:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 2px #000;
}
.dia-left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
</style>
