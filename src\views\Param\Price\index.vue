<script setup>
import {
  ref,
  toRefs,
  getCurrentInstance,
  onMounted,
  nextTick,
  computed
} from 'vue'
import { useGlobalStore } from '@/store/global'
import { useParamStore } from '@/store/module/param'
import { useUserStore } from '@/store/module/user'
import { useLogStore } from '@/store/module/log'
import { useI18n } from 'vue-i18n'
import { useDisplay } from 'vuetify'
import { isBetween, setLeftWidth } from '@/utils'
import { useAllowedDate } from '@/hook/useAllowedDate'
import dayjs, { generateTimePoints, formatDate } from '@/utils/date'
import { isEmpty } from 'lodash-es'
import { useDeviceStore } from '@/store/module/device'
import { useStatisticsStore } from '@/store/module/statistics'

import LineEchart from './lineEchart.vue'
import { ElLoading } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String
  }
})
const { userInfo } = toRefs(useUserStore())
const { mobile } = useDisplay()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { strategyData, countryOptions, countryDatas } = toRefs(useParamStore())
const { treeData } = toRefs(useDeviceStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  keyboardRange,
  isShowKeyboard
} = toRefs(useGlobalStore())
const getLoading = ref()
const getData = () => {
  getLoading.value = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  useParamStore()
    .getStrategyDataFn(JSON.stringify({ name: props.modelValue }))
    .then((res) => {
      countryQueryInfo.value = {
        ...countryQueryInfo.value,
        ...strategyData.value
      }
      getLoading.value.close()
      if (strategyData.value.region != 'UNKNOWN') {
        getCountryData()
        snackbar.value = true
        snackbarText.value = t('调度获取成功')
      } else {
        snackbar.value = true
        snackbarText.value = t('请先设置区域')
      }
      nextTick(() => {
        setLeftWidth('left')
      })
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
/**
 * 获取国家列表
 */
const getCountryList = () => {
  useParamStore()
    .countryListFn()
    .then(() => {
      getData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getCountryList()
const countryQueryInfo = ref({
  region: undefined,
  date: ''
})
const handleCountryChange = () => {}
const isShowDate = ref(false)
const { allowedDates } = useAllowedDate()
const date = ref([])
const handleDateChange = (e) => {
  countryQueryInfo.value.date = formatDate(e, 'YYYY-MM-DD')
  isShowDate.value = false
}
const currentPointArr = ref({
  times: [],
  datas: [],
  currency: '',
  name: '',
  date: ''
})
const schedulePointArr = ref({
  times: [],
  datas: []
})
const getCountryData = () => {
  // let values = Object.values(countryQueryInfo.value)
  // if (values.some((item) => !item)) {
  //   snackbar.value = true
  //   snackbarText.value = t('请选择参数')
  //   return
  // }
  useParamStore()
    .countryDataFn({
      region: countryQueryInfo.value.region,
      date: countryQueryInfo.value.date
    })
    .then(async () => {
      let times = generateTimePoints(
        countryDatas.value.timeInterval,
        'YYYY-MM-DD HH:mm:ss',
        { value: 1, unit: 'minute' }
      )
      // 电价
      currentPointArr.value.times = times
      let priceDatas = []
      countryDatas.value.points.forEach((item) => {
        if (countryDatas.value.resolution == 60) {
          let arr = new Array(60).fill(item.price_amount)
          priceDatas = [...priceDatas, ...arr]
        } else {
          let arr = new Array(15).fill(item.price_amount)
          priceDatas = [...priceDatas, ...arr]
        }
      })
      currentPointArr.value.datas = priceDatas
      currentPointArr.value.currency = countryDatas.value.currency
      currentPointArr.value.name = countryOptions.value.find(
        (item) => item.code == countryQueryInfo.value.region
      ).name
      currentPointArr.value.date = countryQueryInfo.value.date
      // 调度
      schedulePointArr.value.times = times
      let datas = []
      schedulePointArr.value.times.forEach((time, index) => {
        datas.push(0)
        countryQueryInfo.value.scheduleUnits.forEach((item) => {
          if (
            dayjs(time)
              // .utc(time)WS
              // .subtract(2, 'hour')
              .isBetween(item.start, item.end, null, '[)')
          ) {
            datas[index] = item.power
          }
        })
      })
      schedulePointArr.value.datas = datas
      getACPowerData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
/**
 * 获取调度信息
 */
const algorithmOptions = ref([
  // {
  //   value: 'base',
  //   title: 'base'
  // },
  {
    value: 'occupied',
    title: t('常规生成')
  },
  {
    value: 'merge',
    title: t('精确生成')
  }
])
/**
 * 设置策略信息
 */
const setLoading = ref(false)
const setStrategy = () => {
  setLoading.value = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  useParamStore()
    .updateStrategyConfigFn(
      JSON.stringify({
        name: props.modelValue,
        config: {
          ...countryQueryInfo.value,
          scheduleUnits: undefined
        }
      })
    )
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      if (keyboardDialog.value) {
        showKeyboard.value = false
        keyboardDialog.value = false
      }
      setLoading.value.close()
      setTimeout(() => {
        getData()
      }, 1000)
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      setLoading.value.close()
    })
}
/**
 * 键盘
 */
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop, range) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
  keyboardRange.value = range
}
confirmCall.value = () => {
  if (keyboardInputValue.value) {
    const regex = /^-?\d+(\.\d+)?$/
    if (!regex.test(keyboardInputValue.value)) {
      snackbar.value = true
      snackbarText.value = t('只能为数字')
      return
    }
    if (keyboardInputValue.value === '' && !isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      return
    }
  } else {
    if (keyboardInputValue.value === '' && !isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      return
    }
  }
  if (keyboardRange.value) {
    if (
      !isBetween(
        Number(keyboardInputValue.value),
        keyboardRange.value[0],
        keyboardRange.value[1]
      )
    ) {
      snackbar.value = true
      snackbarText.value = t('range', keyboardRange.value)
      return
    }
  }
  countryQueryInfo.value = {
    ...countryQueryInfo.value,
    [currentInput.value]: keyboardInputValue.value
  }
  setStrategy()
}
/**
 * 确认下发弹框
 */
const dialog = ref(false)
const loading = ref(false)
const dialogData = ref({
  value: '',
  prop: ''
})
const handleConfirmClick = () => {
  loading.value = true
  useParamStore()
    .postStrategyCommandFn(
      JSON.stringify({
        paramName: dialogData.value.prop,
        value: dialogData.value.value
      })
    )
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      dialog.value = false
      loading.value = false
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      loading.value = false
    })
}
const handleSendDialogClick = (value, prop) => {
  dialogData.value.value = value
  dialogData.value.prop = prop
  dialog.value = true
}
const handleCancelClick = () => {
  dialog.value = false
  loading.value = false
}

/**
 * 获取功率曲线数据、AC
 */
const powerData = ref([])
const getACPowerData = async () => {
  await useDeviceStore().getDeviceListFn()
  const ACDevice = treeData.value.filter(
    (item) => item.device_class == 'ACHMIDevice'
  )
  let requestParams = []
  ACDevice.forEach((item) => {
    requestParams.push(
      {
        point_id: 'ActivePowerA',
        date: countryQueryInfo.value.date,
        ...item
      },
      {
        point_id: 'ActivePowerB',
        date: countryQueryInfo.value.date,
        ...item
      },
      {
        point_id: 'ActivePowerC',
        date: countryQueryInfo.value.date,
        ...item
      }
    )
  })
  Promise.all([
    useStatisticsStore().getPointDataFn(
      JSON.stringify(
        requestParams.map((item) => {
          return {
            device_id: item.device_id,
            point_id: item.point_id,
            date: item.date
          }
        })
      )
    )
  ]).then((results) => {
    results.forEach((item, resIndex) => {
      if (item?.datas.length) {
        item?.datas.forEach((item2, index2) => {
          let param = requestParams.find(
            (param) =>
              param.point_id == item.pointId[index2].pointId &&
              param.device_name == item.pointId[index2].deviceName
          )
          powerData.value.push({
            ...param,
            datas: item2,
            times: item?.times[index2],
            unit: item.pointId[index2].unit
          })
        })
      }
    })
  })
}
</script>

<template>
  <div :class="[mobile && 'flex-column', 'flex', 'w-100', 'pt-3']">
    <div :style="{ width: '100%' }">
      <div class="mb-4">
        <v-btn
          height="48px"
          class="select-device-btn"
          color="primary"
          @click="getData"
          >{{ $t('更新策略') }}</v-btn
        >
        <v-btn
          height="48px"
          class="ml-4 select-device-btn"
          color="primary"
          @click="setStrategy"
          >{{ $t('下发') }}</v-btn
        >
      </div>
      <v-row>
        <v-col cols="12" lg="12" md="12" sm="12" xs="12">
          <v-row>
            <v-col cols="3">
              <v-autocomplete
                v-model="countryQueryInfo.region"
                item-value="code"
                item-title="name"
                clearable
                :label="$t('选择地区')"
                :placeholder="$t('选择地区')"
                :items="countryOptions"
                variant="outlined"
                @update:modelValue="handleCountryChange"
                hide-details
              ></v-autocomplete>
            </v-col>
            <v-col cols="3">
              <v-menu
                v-model="isShowDate"
                location="bottom"
                :close-on-content-click="false"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-model="countryQueryInfo.date"
                    prepend-inner-icon="mdi-calendar-range"
                    :label="$t('日期')"
                    variant="outlined"
                    hide-details
                    single-line
                    v-bind="props"
                  ></v-text-field>
                </template>

                <v-date-picker
                  v-model="date"
                  show-adjacent-months
                  :allowed-dates="allowedDates"
                  color="primary"
                  elevation="4"
                  class="date-picker"
                  @update:modelValue="handleDateChange"
                ></v-date-picker>
              </v-menu>
            </v-col>
            <v-col cols="3">
              <v-autocomplete
                v-model="countryQueryInfo.algorithm"
                clearable
                :label="$t('策略生成')"
                :placeholder="$t('策略生成')"
                :items="algorithmOptions"
                variant="outlined"
                hide-details
              ></v-autocomplete>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.outterCurrentSOC"
                :reverse="false"
                controlVariant="default"
                label="Current SOC"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :max="100"
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.outterCurrentSOC,
                    'outterCurrentSOC',
                    [0, 100]
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-autocomplete
                v-model="countryQueryInfo.useOutterCurrentSOC"
                clearable
                :label="$t('是否使用currentSOC')"
                :placeholder="$t('是否使用currentSOC')"
                :items="[
                  {
                    title: $t('是'),
                    value: 1
                  },
                  {
                    title: $t('否'),
                    value: 0
                  }
                ]"
                variant="outlined"
                hide-details
              ></v-autocomplete>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.maxSOC"
                :reverse="false"
                controlVariant="default"
                label="Max SOC (%)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :max="100"
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.maxSOC,
                    'maxSOC',
                    [0, 100]
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.minSOC"
                :reverse="false"
                controlVariant="default"
                label="Min SOC (%)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :max="100"
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.minSOC,
                    'minSOC',
                    [0, 100]
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.maxDischgPower"
                :reverse="false"
                controlVariant="default"
                label="Max Discharge Power (kW)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.maxDischgPower,
                    'maxDischgPower'
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.maxChgPower"
                :reverse="false"
                controlVariant="default"
                label="Max Charge Power (kW)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.maxChgPower,
                    'maxChgPower'
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.batteryCapacity"
                :reverse="false"
                controlVariant="default"
                label="Battery Capacity (kWh)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :step="0.1"
                :precision="1"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.batteryCapacity,
                    'batteryCapacity'
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.etaChg"
                :reverse="false"
                controlVariant="default"
                label="Charge Efficiency (0.00~1.00)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                @click:control="
                  handleShow($event, countryQueryInfo.etaChg, 'etaChg', [0, 1])
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.etaDis"
                :reverse="false"
                controlVariant="default"
                label="Discharge Efficiency (0.00~1.00)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                @click:control="
                  handleShow($event, countryQueryInfo.etaDis, 'etaDis', [0, 1])
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-number-input
                v-model="countryQueryInfo.priceThresholdRatio"
                :reverse="false"
                controlVariant="default"
                label="Price Threshold Ratio (0.00~1.00)"
                :hideInput="false"
                inset
                variant="outlined"
                hide-details
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                @click:control="
                  handleShow(
                    $event,
                    countryQueryInfo.priceThresholdRatio,
                    'priceThresholdRatio',
                    [0, 1]
                  )
                "
              ></v-number-input>
            </v-col>
            <v-col cols="3">
              <v-autocomplete
                v-model="countryQueryInfo.noScheduleAction"
                clearable
                :label="$t('无调度行为')"
                :placeholder="$t('无调度行为')"
                :items="[
                  {
                    title: $t('下发关机'),
                    value: 1
                  },
                  {
                    title: $t('设置0功率'),
                    value: 0
                  }
                ]"
                variant="outlined"
                hide-details
              ></v-autocomplete>
            </v-col>
            <v-col cols="3">
              <div class="flex align-center row">
                <div
                  class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center"
                >
                  <div class="mr-1">{{ $t('系统启动') }}</div>
                  <v-tooltip :text="$t('整个系统总开关。')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-icon
                        icon="mdi-help-circle"
                        size="small"
                        v-bind="props"
                      ></v-icon> </template
                  ></v-tooltip>
                </div>
                <div class="pl-0 flex">
                  <v-btn
                    class="mr-2"
                    @click="handleSendDialogClick(1, 'systemSwitch')"
                    >{{ $t('开') }}</v-btn
                  >
                  <v-btn @click="handleSendDialogClick(0, 'systemSwitch')">{{
                    $t('关')
                  }}</v-btn>
                </div>
              </div>
            </v-col>
            <v-col cols="3">
              <div class="flex align-center row">
                <div
                  class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center"
                >
                  <div class="mr-1">{{ $t('上下高压') }}</div>
                  <v-tooltip
                    :text="$t('控制电池高压开关状态。')"
                    location="top"
                  >
                    <template v-slot:activator="{ props }">
                      <v-icon
                        icon="mdi-help-circle"
                        size="small"
                        v-bind="props"
                      ></v-icon> </template
                  ></v-tooltip>
                </div>
                <div class="pl-0 flex">
                  <v-btn
                    class="mr-2"
                    @click="handleSendDialogClick(1, 'bmsSwitch')"
                    >{{ $t('开') }}</v-btn
                  >
                  <v-btn @click="handleSendDialogClick(0, 'bmsSwitch')">{{
                    $t('关')
                  }}</v-btn>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <LineEchart
        style="height: 490px; width: 100%"
        v-model:lineData="currentPointArr"
        v-model:scheduleData="schedulePointArr"
        v-model:powerData="powerData"
        v-if="!isEmpty(currentPointArr.times)"
        class="mt-2"
      />
    </div>

    <v-dialog v-model="dialog" width="auto" persistent>
      <v-card width="480" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-4">{{
          $t('系统提示')
        }}</v-card-title>
        <div class="flex justify-center align-center">
          <v-icon icon="mdi-alert-circle" size="small"></v-icon>
          <div>{{ $t('确认下发该参数？') }}</div>
        </div>
        <div class="d-flex justify-center mt-4">
          <v-btn
            class="mt-2 mr-4 px-8"
            height="50"
            @click="handleCancelClick"
            >{{ $t('取消') }}</v-btn
          >
          <v-btn
            class="mt-2 px-8"
            height="50"
            :loading="loading"
            color="primary"
            @click="handleConfirmClick"
            >{{ $t('确定') }}</v-btn
          >
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
:deep(.v-col) {
  padding: 5px 12px !important;
}
</style>
