<script setup>
import { ref, toRefs, watchEffect, onUnmounted, watch, computed } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { isEmpty } from 'lodash-es'

import DataItem from '@/components/data-item'

const emits = defineEmits(['backClick'])

const { clusterData, clusterPointData, batteryTypeDeviceId } = toRefs(
  useDeviceStore()
)
const props = defineProps({
  deviceId: [String, Number],
  isTree: Boolean
})
const isCluster = ref(1)
const clusterTime = ref(null)
const pointTime = ref(null)
watch(
  () => props.isTree,
  () => {
    if (props.isTree) {
      clearInterval(clusterTime.value)
      clearInterval(pointTime.value)
      getData()
    }
  }
)
const getData = () => {
  clearInterval(clusterTime.value)
  clearInterval(pointTime.value)
  useDeviceStore().batteryClusterFn({
    deviceId: props.deviceId
  })
  isCluster.value = 1
  clusterTime.value = setInterval(() => {
    if (isCluster.value == 1)
      useDeviceStore().batteryClusterFn({
        deviceId: props.deviceId
      })
  }, 2000)
}
getData()
watch(
  () => props.deviceId,
  (newValue, oldValue) => {
    if (!newValue) return
    let isBT =
      batteryTypeDeviceId.value.findIndex((item) => item == newValue) != -1
    if (!isBT) return
    clearInterval(clusterTime.value)
    clearInterval(pointTime.value)
    getData()
  }
)
const packData = ref({})
const packCalcData = ref([
  {
    value: 'maxV',
    title: '最高单体电压'
  },
  {
    value: 'maxT',
    title: '最高单体温度'
  },
  {
    value: 'minV',
    title: '最低单体电压'
  },
  {
    value: 'minT',
    title: '最低单体温度'
  }
])
const handleClusterItemClick = (cluster_name) => {
  getPointData(cluster_name)
  clearInterval(pointTime.value)
  pointTime.value = setInterval(() => {
    if (isCluster.value != 1) getPointData(cluster_name)
  }, 2000)
}
const cell = ref(0)
const getPointData = (cluster_name) => {
  useDeviceStore()
    .batteryDataFn({
      deviceId: props.deviceId,
      clusterName: cluster_name
    })
    .then((res) => {
      cell.value = res?.cell
      if (res?.cell == 0) {
        packData.value = {}
        pointData.value = clusterPointData.value[cluster_name]
        isCluster.value = 3
      } else {
        packData.value = {
          ...packData.value,
          ...clusterPointData.value[cluster_name]
        }
        if (isCluster.value != 3) isCluster.value = 2
      }
    })
}
const pointData = ref()
const handlePackItemClick = (key) => {
  pointData.value = packData.value[key]
  isCluster.value = 3
}

const handleBackClick = () => {
  if (isCluster.value == 1) {
    emits('backClick')
  } else if (isCluster.value == 2) {
    isCluster.value = 1
  } else if (isCluster.value == 3) {
    isCluster.value = isEmpty(packData.value) ? 1 : 2
  }
}

onUnmounted(() => {
  clearInterval(clusterTime.value)
  clearInterval(pointTime.value)
})
// 有换行符
const lineBreak = computed(() => {
  return (string) => {
    const res = {
      text: '',
      status: 0
    }
    if (typeof string == 'string') {
      if (string.indexOf('\n') !== -1) {
        res = {
          text: string?.replace(/\n/g, '<br />'),
          status: 1
        }
      } else {
        res = {
          text: string,
          status: 0
        }
      }
      console.log(res)
      return string.indexOf('\n') !== -1
        ? `${string?.replace(/\n/g, '<br />')}`
        : string
    } else {
      res = {
        text: string,
        status: 0
      }
      return string
    }
  }
})

defineExpose({
  getData
})
</script>

<template>
  <div class="pa-4 w-100">
    <div v-if="isCluster == 1" class="flex flex-wrap w-100">
      <template v-if="clusterData.length">
        <v-hover v-for="item in clusterData" :key="item.cluster_name">
          <template v-slot:default="{ isHovering, props }">
            <v-card
              variant="outlined"
              class="rounded-lg pa-4 ma-4 cursor-pointer"
              style="width: 300px; height: 280px; position: relative"
              v-bind="props"
              :elevation="isHovering ? 4 : 0"
              @click="handleClusterItemClick(item.cluster_name)"
            >
              <div class="flex align-center mb-4">
                <img
                  src="../../assets/img/device-default.png"
                  alt=""
                  style="width: 25px"
                />
                <div class="ml-2 font-600">{{ item.cluster_name }}</div>
              </div>
              <div class="flex align-center justify-center">
                <img
                  src="../../assets//img/default.png"
                  alt=""
                  style="width: 200px; height: 200px"
                />
              </div>
            </v-card>
          </template>
        </v-hover>
      </template>
      <empty v-else />
    </div>
    <div v-else-if="isCluster == 2" class="flex flex-wrap w-100">
      <template v-if="!isEmpty(packData)">
        <v-card
          variant="outlined"
          v-for="(value, key) in packData"
          class="rounded-lg pa-4 ma-4"
          style="width: 300px; height: 250px"
          :key="key"
          @click="handlePackItemClick(key)"
        >
          <div class="flex align-center justify-space-between mb-2 w-100">
            <div class="flex align-center" style="width: 87%">
              <img
                src="../../assets/img/device-module.png"
                alt=""
                style="width: 25px"
              />
              <div class="ml-2 font-600 line1" style="width: 80%" :title="key">
                {{ key }}
              </div>
            </div>
            <img
              src="../../assets/img/more.png"
              alt=""
              style="width: 30px; height: 20px"
            />
          </div>
          <div
            v-for="item in packCalcData"
            :key="item.value"
            class="py-3 flex align-center"
          >
            <img
              src="../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
            />
            <div
              style="width: 90%"
              class="line1"
              :title="`${$t(item.title)}：${value[item.value]?.value} ${
                value[item.value]?.units
              }`"
            >
              {{ $t(item.title) }}：{{ value[item.value]?.value }}
              {{ value[item.value]?.units }}
            </div>
          </div>
        </v-card>
      </template>
      <empty v-else />
    </div>
    <div v-if="isCluster == 3" class="w-100">
      <template v-if="!isEmpty(pointData)">
        <div v-if="cell == 0">
          <div class="text-body-1 mb-4 d-flex align-center">
            <v-icon icon="mdi-database" color="warning" class="mr-1"></v-icon
            >{{ $t('运行数据') }}
          </div>
          <v-row>
            <v-col
              cols="12"
              lg="4"
              md="6"
              sm="12"
              v-for="item in pointData"
              :key="item.point_id"
            >
              <v-row>
                <v-col cols="12">
                  <!-- <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-card
                        variant="tonal"
                        class="py-4 px-4"
                        v-bind="props"
                        :elevation="isHovering ? 4 : 0"
                        color="grey-darken-1"
                      >
                        <div
                          class="flex align-center justify-center"
                          :style="{
                            color: '#000',
                            height: '40px'
                          }"
                        >
                          <img
                            src="../../assets/img/circle.png"
                            alt=""
                            style="width: 20px; height: 20px"
                            class="mr-2"
                          />
                          <span>{{ item.point_name }}</span>
                        </div>
                        <div
                          class="font-600 my-1 flex justify-center overflow-hidden"
                          :style="{
                            color: '#ffaa43',
                            height: '30px',
                            lineHeight: '30px'
                          }"
                        >
                          <div v-html="lineBreak(item.value)"></div>
                          {{ item.units }}
                        </div>
                        <div
                          class="w-100"
                          :style="{
                            color: '#000',
                            textAlign: 'center',
                            height: '30px',
                            lineHeight: '30px'
                          }"
                        >
                          {{ item.time ? item.time : '--' }}
                        </div>
                      </v-card>
                    </template>
                  </v-hover> -->
                  <data-item :item="item" />
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
        <div v-else class="flex flex-wrap justify-space-evenly">
          <div
            v-for="(value, key) in pointData"
            :key="key"
            style="width: 122px"
            class="ma-2"
          >
            <v-hover
              v-if="packCalcData.findIndex((item) => item.value == key) == -1"
            >
              <template v-slot:default="{ isHovering, props }">
                <div
                  v-bind="props"
                  style="position: relative; width: 100%; height: 150px"
                  class="flex flex-column align-center"
                >
                  <div
                    style="
                      width: 40%;
                      height: 15%;
                      background-color: #e9e9e9;
                      border-radius: 20px;
                      position: absolute;
                      top: 0;
                      left: 30%;
                    "
                    class="flex flex-justify-center align-center"
                    :class="{ 'elevation-4': isHovering }"
                  >
                    <span v-if="isHovering" style="font-size: 14px">{{
                      key
                    }}</span>
                  </div>
                  <div
                    style="
                      width: 100%;
                      height: 85%;
                      background-color: #e9e9e9;
                      border-radius: 20px;
                      margin-top: 10%;
                      padding-top: 10%;
                    "
                    class="flex flex-column align-center"
                    :class="{ 'elevation-4': isHovering }"
                  >
                    <div class="flex align-center">
                      <img
                        src="../../assets/img/cellV.svg"
                        alt=""
                        style="width: 18px"
                      />
                      <div style="color: #757575">{{ $t('电压') }}</div>
                    </div>
                    <div>
                      <span style="font-weight: 600">{{ value.voltage }}</span>
                      mV
                    </div>
                    <div class="flex align-center">
                      <img
                        src="../../assets/img/cellT.svg"
                        alt=""
                        style="width: 18px"
                      />
                      <div style="color: #757575">{{ $t('温度') }}</div>
                    </div>
                    <div>
                      <span style="font-weight: 600">{{
                        value.temperature ? value.temperature : '--'
                      }}</span>
                      ℃
                    </div>
                  </div>
                </div>
              </template></v-hover
            >
          </div>
        </div>
      </template>
      <empty v-else />
    </div>

    <v-fab
      color="primary"
      icon="mdi-undo-variant"
      style="bottom: 120px; right: 100px; position: fixed"
      size="x-large"
      @click="handleBackClick"
    ></v-fab>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-card--variant-outlined) {
  border: thin solid #ccc;
}
</style>
