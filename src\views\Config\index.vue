<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 16:05:52
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-17 14:29:51
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  toRefs,
  getCurrentInstance,
  nextTick,
  onMounted,
  watch
} from 'vue'
import { useUserStore } from '@/store/module/user'
import { useDeviceConfigStore } from '@/store/module/deviceConfig'

import HomeConfig from './home.vue'
import SystemConfig from './system.vue'
import Setting from './setting.vue'
import Device from './device.vue'
import DeviceConfig from './deviceConfig.vue'
import DatabaseMan from './databaseMan/index'
import BackLog from './BackLog/index.vue'

useDeviceConfigStore().isEdit = false
const { proxy } = getCurrentInstance()
const tab = ref(1)
const { userInfo } = toRefs(useUserStore())
const handleTabChange = () => {
  if (tab.value == 1) {
    proxy.$refs.systemRef.getData()
  } else if (tab.value == 2) {
    nextTick(() => proxy.$refs.homeRef.getData())
  } else if (tab.value == 3) {
    nextTick(() => proxy.$refs.setRef.getData())
  } else if (tab.value == 4) {
    nextTick(() => proxy.$refs.deviceRef.getData())
  } else if (tab.value == 7) {
    nextTick(() => backLogRef.value.webSocketInit())
  }
}
onMounted(() => {
  proxy.$refs.systemRef.getData()
})

const oldTab = ref(1)
const backLogRef = ref(null)
watch(tab, (val, oldVal) => {
  if (oldVal == 7 && val != oldVal) {
    backLogRef.value.dialog = true
  }
})
const confirmLeave = () => {}
const cancelLeave = () => {
  tab.value = 7
}
</script>

<template>
  <div class="pa-6 w-100 overflow-hidden d-flex flex-column">
    <div class="px-2">
      <v-card class="pa-4 w-100 rounded-lg no-scrollbar mb-4" elevation="4">
        <div class="d-flex justify-between align-center">
          <div class="text-h6">{{ $t('配置中心') }}</div>
          <div class="d-flex align-center"></div>
        </div>
      </v-card>
    </div>
    <v-tabs
      v-model="tab"
      color="secondary"
      class="px-3"
      @update:modelValue="handleTabChange"
    >
      <v-tab :value="1">{{ $t('项目配置') }}</v-tab>
      <v-tab :value="2" v-if="userInfo.permission_level != 2">{{
        $t('首页配置')
      }}</v-tab>
      <v-tab :value="3">{{ $t('系统设置') }}</v-tab>
      <v-tab :value="4">{{ $t('设备展示配置') }}</v-tab>
      <v-tab :value="5" v-if="userInfo.permission_level != 2">{{
        $t('系统文件配置')
      }}</v-tab>
      <v-tab :value="6" v-if="userInfo.permission_level == 4">{{
        $t('数据库管理')
      }}</v-tab>
      <v-tab :value="7" v-if="userInfo.permission_level == 4">{{
        $t('后台日志')
      }}</v-tab>
    </v-tabs>

    <v-tabs-window v-model="tab" class="mt-4">
      <v-tabs-window-item :value="1">
        <SystemConfig ref="systemRef" />
      </v-tabs-window-item>
      <v-tabs-window-item :value="2">
        <HomeConfig ref="homeRef" />
      </v-tabs-window-item>
      <v-tabs-window-item :value="3">
        <Setting ref="setRef" />
      </v-tabs-window-item>
      <v-tabs-window-item :value="4">
        <Device ref="deviceRef" />
      </v-tabs-window-item>
      <v-tabs-window-item :value="5">
        <DeviceConfig />
      </v-tabs-window-item>
      <v-tabs-window-item :value="6">
        <DatabaseMan />
      </v-tabs-window-item>
      <v-tabs-window-item :value="7">
        <BackLog
          ref="backLogRef"
          @confirmLeave="confirmLeave"
          @cancelLeave="cancelLeave"
        />
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-window) {
  overflow: visible;
}
</style>
