<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-17 14:35:20
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 16:35:06
 * @FilePath: \ems_manage\src\views\Config\BackLog\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { onMounted, ref, watch, toRefs } from 'vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'

const emits = defineEmits(['confirmLeave', 'cancelLeave'])
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const { t } = useI18n()

const ws = ref(null)
const logData = ref([])
watch(
  () => logData.value,
  (val) => {
    if (logData.value.length >= 20000) {
      logData.value = logData.value.slice(val.length - 20000)
    }
  },
  {
    deep: true
  }
)
const webSocketInit = () => {
  let host = location.host
  if (import.meta.env.MODE == 'development') {
    ws.value = new WebSocket('/ws/realtime/logs')
  } else {
    ws.value = new WebSocket(`ws://${host}/api/realtime/logs`)
  }

  ws.value.onopen = (event) => {
    snackbar.value = true
    snackbarText.value = t('建立连接')
    console.log('建立连接')
  }
  ws.value.onclose = (event) => {
    snackbar.value = true
    snackbarText.value = t('连接关闭')
    console.log('连接关闭')
  }
  ws.value.onmessage = (event) => {
    console.log('业务处理中', event)
    try {
      let data = JSON.parse(event.data)
      if (data.logLevelSet) {
        logLevel.value = data.logLevelSet.logLevel
      }
    } catch (error) {
      logData.value.push(event.data)
      scrollToBottom()
    }
  }
  ws.value.onerror = () => {
    console.log('连接出错啦')
    snackbar.value = true
    snackbarText.value = t('连接出错啦')
  }
  window.onbeforeunload = () => {
    ws.value.close()
  }
  // 监听可能发生的错误
  ws.value.addEventListener('error', function (event) {
    console.log('WebSocket error: ', event)
  })
}
const scrollToBottom = () => {
  const messagesContainer = document.querySelector('.log-cont')
  if (messagesContainer)
    messagesContainer.scrollTop = messagesContainer.scrollHeight
}

const logLevel = ref()
const logLevelData = ref([
  {
    value: 0,
    title: 'trace'
  },
  {
    value: 1,
    title: 'debug'
  },
  {
    value: 2,
    title: 'info'
  },
  {
    value: 3,
    title: 'warn'
  },
  {
    value: 4,
    title: 'err'
  },
  {
    value: 5,
    title: 'critical'
  }
])
const handleLogLevelChange = () => {
  ws.value.send(
    JSON.stringify({
      code: 0,
      logLevelSet: {
        logLevel: logLevel.value
      }
    })
  )
}

/**
 * 是否离开
 */
const dialog = ref(false)
const router = useRouter()
const handleDialogConfirm = () => {
  if (isToPath.value) isLeaving.value = true
  dialog.value = false
  logData.value = []
  ws.value.close()
  ws.value = null
  router.push({ path: isToPath.value })
  isToPath.value = ''
  emits('confirmLeave')
}
const handleDialogCancel = () => {
  dialog.value = false
  isToPath.value = ''
  emits('cancelLeave')
}

const isLeaving = ref(false) // 用于跟踪是否正在离开路由的标志位
const isToPath = ref()
onBeforeRouteLeave((to, from, next) => {
  isToPath.value = to.path
  if (isLeaving.value) {
    // 如果已经标记为正在离开，则允许离开，否则询问用户是否确定要离开
    next() // 允许离开，因为用户已经确认了离开操作。通常在用户点击确认后设置isLeaving.value = true;
  } else {
    dialog.value = true
    next(false)
  }
})

defineExpose({
  dialog,
  webSocketInit
})
</script>

<template>
  <div class="w-100 px-2 h-100">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <v-select
        v-model="logLevel"
        item-value="value"
        clearable
        :label="$t('日志等级')"
        :placeholder="$t('日志等级')"
        :items="logLevelData"
        variant="outlined"
        class="w-100 mr-4 mt-2"
        hide-details
        @update:modelValue="handleLogLevelChange"
      ></v-select>
      <div
        class="log-cont pa-4 mt-4 rounded-lg flex flex-column overflow-y-scroll"
      >
        <div class="flex flex-column message">
          <div v-for="item in logData" :key="item" v-html="item"></div>
        </div>
      </div>
    </v-card>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="540" class="pa-4 rounded-lg">
        <v-card-title>{{ $t('系统提示') }}</v-card-title>
        <v-card-text>{{ $t('是否离开此页面？') }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="handleDialogCancel"
          >
            {{ $t('取消') }}
          </v-btn>
          <v-btn color="primary" variant="text" @click="handleDialogConfirm">
            {{ $t('确定') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.log-cont {
  background-color: #000;
  color: #fff;
  height: 550px;
}
</style>
