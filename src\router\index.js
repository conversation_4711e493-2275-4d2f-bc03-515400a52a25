/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:26:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-20 15:48:26
 * @FilePath: \ems_manage\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createWebHashHistory, createRouter } from 'vue-router'
import { useUserStore } from '@/store/module/user'
import { i18n } from '@/locale'

const staticRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/Error/403.vue'),
    meta: {
      title: '403'
    }
  },
  {
    path: '/webPreview',
    name: 'WebPreview',
    component: () => import('@/views/Dashboard/webPreview.vue'),
    meta: {
      title: '通讯拓扑图'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: 'NotFound'
    }
  }
]

export const routes = [
  {
    path: '/',
    component: () => import('@/layout'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '数据概括',
          icon: 'mdi-home',
          sort: 1
        }
      },
      // {
      //   path: '/test',
      //   name: 'Test',
      //   component: () => import('@/views/test.vue'),
      //   meta: {
      //     roles: [1],
      //     title: '测试',
      //     icon: 'mdi-home',
      //     sort: 8
      //   }
      // },
      {
        path: '/details',
        name: 'Details',
        component: () => import('@/views/Details/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '设备详情',
          icon: 'mdi-monitor',
          sort: 2
        }
      },
      {
        path: '/analyse',
        name: 'Analyse',
        component: () => import('@/views/Analyse/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '数据分析',
          icon: 'mdi-chart-line',
          sort: 4
        }
      },
      {
        path: '/electric',
        name: 'Electric',
        component: () => import('@/views/Electric/index.vue'),
        meta: {
          roles: [4, 3, 2, 1],
          title: '电量统计',
          icon: 'mdi-chart-bar',
          sort: 3
        }
      },
      // {
      //   path: '/param',
      //   name: 'Param',
      //   component: () => import('@/views/Param/index.vue'),
      //   meta: {
      //     roles: [1, 2, 3],
      //     title: '参数设置',
      //     icon: 'mdi-cog',
      //     sort: 5
      //   }
      // },
      // {
      //   path: '/strategy',
      //   name: 'Strategy',
      //   component: () => import('@/views/Param/strategy.vue'),
      //   meta: {
      //     roles: [1, 2, 3],
      //     title: '策略管理',
      //     icon: 'mdi-strategy',
      //     sort: 5
      //   }
      // },
      {
        path: '/strategy',
        name: 'OperationStrategy',
        component: () => import('@/views/Param/strategyControl.vue'),
        // component: () => import('@/views/Param/strategy308.vue'),
        // component: () => import('@/views/Param/operation.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '运行策略',
          icon: 'mdi-strategy',
          sort: 5
        }
      },
      {
        path: '/log',
        name: 'Log',
        component: () => import('@/views/Log/index.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '日志管理',
          icon: 'mdi-book',
          sort: 6
        }
      },
      {
        path: '/config',
        name: 'Config',
        component: () => import('@/views/Config/index.vue'),
        meta: {
          roles: [4, 3],
          title: '配置中心',
          icon: 'mdi-cogs',
          sort: 6
        },
        children: [
          {
            path: '/deviceConfig',
            name: 'DeviceConfig',
            component: () => import('@/views/Config/DeviceConfig/index.vue'),
            meta: {
              roles: [4, 3],
              title: '设备管理',
              icon: 'mdi-cogs',
              sort: 6,
              isHidden: 'false'
            }
          },
        ]
      },
      {
        path: '/user',
        name: 'User',
        component: () => import('@/views/Login/user.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '用户管理',
          icon: 'mdi-account',
          sort: 7
        }
      },
      {
        path: '/system',
        name: 'System',
        component: () => import('@/views/System/index.vue'),
        meta: {
          roles: [4, 3, 2],
          title: '系统信息',
          icon: 'mdi-information',
          sort: 8
        }
      },
    ]
  },
  {
    path: '/editor',
    name: 'Editor',
    component: () => import('@/views/Editor/index.vue'),
    meta: {
      roles: [4],
      title: '编辑图',
      icon: 'mdi-palette',
      sort: 7
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: [...staticRoutes, ...routes],
  scrollBehavior() {
    return { top: 0 }
  }
})

router.beforeEach((to, from) => {
  document.timeline
  const user = useUserStore()
  if (!user.isLogin && to.path !== '/login') {
    return '/login'
  } else {
    if (staticRoutes.findIndex(item => item.path == to.path) !== -1) {
      return
    } else if (
      to.meta?.roles?.findIndex(
        (item) => item == user.userInfo.permission_level
      ) !== -1
    ) {
      if (to.path == '/login') {
        user.loginOut()
      }
      return
    } else {
      return '/403'
    }
  }
})

export default router
